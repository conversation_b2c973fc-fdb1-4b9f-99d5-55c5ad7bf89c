package com.foodorder.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.foodorder.dto.auth.LoginRequest;
import com.foodorder.dto.auth.RegisterRequest;
import com.foodorder.entity.User;
import com.foodorder.entity.UserRole;
import com.foodorder.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.util.Set;

import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
class AuthControllerIntegrationTest {
    
    @Autowired
    private WebApplicationContext context;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private MockMvc mockMvc;
    
    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders
            .webAppContextSetup(context)
            .apply(springSecurity())
            .build();
        
        // 清理测试数据
        userRepository.deleteAll();
    }
    
    @Test
    void testRegister_Success() throws Exception {
        // Given
        RegisterRequest request = new RegisterRequest();
        request.setUsername("testuser");
        request.setPassword("password123");
        request.setConfirmPassword("password123");
        request.setEmail("<EMAIL>");
        request.setRealName("Test User");
        
        // When & Then
        mockMvc.perform(post("/api/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("注册成功"));
        
        // 验证用户已保存到数据库
        assert userRepository.findByUsername("testuser").isPresent();
    }
    
    @Test
    void testRegister_UsernameExists() throws Exception {
        // Given - 先创建一个用户
        User existingUser = new User();
        existingUser.setUsername("testuser");
        existingUser.setEmail("<EMAIL>");
        existingUser.setPassword(passwordEncoder.encode("password"));
        existingUser.setRoles(Set.of(UserRole.CUSTOMER));
        existingUser.setEnabled(true);
        userRepository.save(existingUser);
        
        RegisterRequest request = new RegisterRequest();
        request.setUsername("testuser"); // 相同的用户名
        request.setPassword("password123");
        request.setConfirmPassword("password123");
        request.setEmail("<EMAIL>");
        request.setRealName("Test User");
        
        // When & Then
        mockMvc.perform(post("/api/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("用户名已存在"));
    }
    
    @Test
    void testRegister_EmailExists() throws Exception {
        // Given - 先创建一个用户
        User existingUser = new User();
        existingUser.setUsername("existinguser");
        existingUser.setEmail("<EMAIL>");
        existingUser.setPassword(passwordEncoder.encode("password"));
        existingUser.setRoles(Set.of(UserRole.CUSTOMER));
        existingUser.setEnabled(true);
        userRepository.save(existingUser);
        
        RegisterRequest request = new RegisterRequest();
        request.setUsername("testuser");
        request.setPassword("password123");
        request.setConfirmPassword("password123");
        request.setEmail("<EMAIL>"); // 相同的邮箱
        request.setRealName("Test User");
        
        // When & Then
        mockMvc.perform(post("/api/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpected(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("邮箱已被注册"));
    }
    
    @Test
    void testRegister_PasswordMismatch() throws Exception {
        // Given
        RegisterRequest request = new RegisterRequest();
        request.setUsername("testuser");
        request.setPassword("password123");
        request.setConfirmPassword("differentPassword"); // 不匹配的密码
        request.setEmail("<EMAIL>");
        request.setRealName("Test User");
        
        // When & Then
        mockMvc.perform(post("/api/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("两次输入的密码不一致"));
    }
    
    @Test
    void testLogin_Success() throws Exception {
        // Given - 先创建一个用户
        User user = new User();
        user.setUsername("testuser");
        user.setEmail("<EMAIL>");
        user.setPassword(passwordEncoder.encode("password123"));
        user.setRoles(Set.of(UserRole.CUSTOMER));
        user.setEnabled(true);
        userRepository.save(user);
        
        LoginRequest request = new LoginRequest();
        request.setUsernameOrEmail("testuser");
        request.setPassword("password123");
        
        // When & Then
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.token").exists())
                .andExpect(jsonPath("$.data.username").value("testuser"));
    }
    
    @Test
    void testLogin_WithEmail() throws Exception {
        // Given - 先创建一个用户
        User user = new User();
        user.setUsername("testuser");
        user.setEmail("<EMAIL>");
        user.setPassword(passwordEncoder.encode("password123"));
        user.setRoles(Set.of(UserRole.CUSTOMER));
        user.setEnabled(true);
        userRepository.save(user);
        
        LoginRequest request = new LoginRequest();
        request.setUsernameOrEmail("<EMAIL>"); // 使用邮箱登录
        request.setPassword("password123");
        
        // When & Then
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.token").exists())
                .andExpect(jsonPath("$.data.username").value("testuser"));
    }
    
    @Test
    void testLogin_WrongPassword() throws Exception {
        // Given - 先创建一个用户
        User user = new User();
        user.setUsername("testuser");
        user.setEmail("<EMAIL>");
        user.setPassword(passwordEncoder.encode("password123"));
        user.setRoles(Set.of(UserRole.CUSTOMER));
        user.setEnabled(true);
        userRepository.save(user);
        
        LoginRequest request = new LoginRequest();
        request.setUsernameOrEmail("testuser");
        request.setPassword("wrongpassword");
        
        // When & Then
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("用户名或密码错误"));
    }
    
    @Test
    void testLogin_UserNotFound() throws Exception {
        // Given
        LoginRequest request = new LoginRequest();
        request.setUsernameOrEmail("nonexistent");
        request.setPassword("password123");
        
        // When & Then
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("用户名或密码错误"));
    }
    
    @Test
    void testLogin_UserDisabled() throws Exception {
        // Given - 创建一个被禁用的用户
        User user = new User();
        user.setUsername("testuser");
        user.setEmail("<EMAIL>");
        user.setPassword(passwordEncoder.encode("password123"));
        user.setRoles(Set.of(UserRole.CUSTOMER));
        user.setEnabled(false); // 禁用用户
        userRepository.save(user);
        
        LoginRequest request = new LoginRequest();
        request.setUsernameOrEmail("testuser");
        request.setPassword("password123");
        
        // When & Then
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("账户已被禁用"));
    }
    
    @Test
    void testRegister_InvalidInput() throws Exception {
        // Given - 缺少必填字段
        RegisterRequest request = new RegisterRequest();
        request.setUsername(""); // 空用户名
        request.setPassword("123"); // 密码太短
        request.setEmail("invalid-email"); // 无效邮箱格式
        
        // When & Then
        mockMvc.perform(post("/api/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }
}
