package com.foodorder.service;

import com.foodorder.entity.*;
import com.foodorder.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class DataAnalyticsService {
    
    private static final Logger logger = LoggerFactory.getLogger(DataAnalyticsService.class);
    
    @Autowired
    private OrderRepository orderRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private MealRepository mealRepository;
    
    @Autowired
    private RatingRepository ratingRepository;
    
    @Autowired
    private MerchantRepository merchantRepository;
    
    /**
     * 获取销售数据统计
     */
    @Cacheable(value = "analytics", key = "'sales_stats_' + #days")
    public Map<String, Object> getSalesStatistics(int days) {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusDays(days);
        
        List<Order> orders = orderRepository.findByCreatedAtBetween(startDate, endDate);
        List<Order> completedOrders = orders.stream()
            .filter(order -> order.getStatus() == OrderStatus.DELIVERED)
            .collect(Collectors.toList());
        
        Map<String, Object> stats = new HashMap<>();
        
        // 基础统计
        stats.put("totalOrders", orders.size());
        stats.put("completedOrders", completedOrders.size());
        stats.put("cancelledOrders", orders.stream()
            .filter(order -> order.getStatus() == OrderStatus.CANCELLED)
            .count());
        
        // 收入统计
        BigDecimal totalRevenue = completedOrders.stream()
            .map(Order::getTotalAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        stats.put("totalRevenue", totalRevenue);
        
        BigDecimal averageOrderValue = completedOrders.isEmpty() ? BigDecimal.ZERO :
            totalRevenue.divide(BigDecimal.valueOf(completedOrders.size()), 2, RoundingMode.HALF_UP);
        stats.put("averageOrderValue", averageOrderValue);
        
        // 日销售趋势
        Map<String, BigDecimal> dailyRevenue = getDailyRevenueTrend(completedOrders, days);
        stats.put("dailyRevenueTrend", dailyRevenue);
        
        // 日订单趋势
        Map<String, Long> dailyOrders = getDailyOrderTrend(orders, days);
        stats.put("dailyOrderTrend", dailyOrders);
        
        // 完成率
        double completionRate = orders.isEmpty() ? 0.0 : 
            (double) completedOrders.size() / orders.size() * 100;
        stats.put("completionRate", Math.round(completionRate * 100.0) / 100.0);
        
        return stats;
    }
    
    /**
     * 获取用户行为分析
     */
    @Cacheable(value = "analytics", key = "'user_behavior_' + #days")
    public Map<String, Object> getUserBehaviorAnalysis(int days) {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusDays(days);
        
        Map<String, Object> analysis = new HashMap<>();
        
        // 用户活跃度分析
        List<User> allUsers = userRepository.findAll();
        long activeUsers = allUsers.stream()
            .filter(user -> user.getLastLoginAt() != null && 
                user.getLastLoginAt().isAfter(startDate))
            .count();
        
        analysis.put("totalUsers", allUsers.size());
        analysis.put("activeUsers", activeUsers);
        analysis.put("activeUserRate", allUsers.isEmpty() ? 0.0 : 
            (double) activeUsers / allUsers.size() * 100);
        
        // 新用户注册趋势
        Map<String, Long> registrationTrend = getUserRegistrationTrend(days);
        analysis.put("registrationTrend", registrationTrend);
        
        // 用户订单分布
        Map<String, Long> orderDistribution = getUserOrderDistribution();
        analysis.put("orderDistribution", orderDistribution);
        
        // 用户评分行为
        List<Rating> recentRatings = ratingRepository.findByCreatedAtBetween(startDate, endDate);
        analysis.put("totalRatings", recentRatings.size());
        
        if (!recentRatings.isEmpty()) {
            BigDecimal avgRating = recentRatings.stream()
                .map(Rating::getScore)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .divide(BigDecimal.valueOf(recentRatings.size()), 2, RoundingMode.HALF_UP);
            analysis.put("averageRating", avgRating);
        }
        
        // 评分分布
        Map<String, Long> ratingDistribution = getRatingDistribution(recentRatings);
        analysis.put("ratingDistribution", ratingDistribution);
        
        return analysis;
    }
    
    /**
     * 获取商品销售分析
     */
    @Cacheable(value = "analytics", key = "'meal_sales_' + #days")
    public Map<String, Object> getMealSalesAnalysis(int days) {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusDays(days);
        
        List<Order> completedOrders = orderRepository.findByStatusAndCreatedAtBetween(
            OrderStatus.DELIVERED, startDate, endDate);
        
        Map<String, Object> analysis = new HashMap<>();
        
        // 商品销量统计
        Map<Long, Integer> mealSalesCount = new HashMap<>();
        Map<Long, BigDecimal> mealSalesRevenue = new HashMap<>();
        
        for (Order order : completedOrders) {
            for (OrderItem item : order.getOrderItems()) {
                Long mealId = item.getMeal().getId();
                mealSalesCount.merge(mealId, item.getQuantity(), Integer::sum);
                mealSalesRevenue.merge(mealId, item.getSubtotal(), BigDecimal::add);
            }
        }
        
        // 热销商品TOP10
        List<Map<String, Object>> topSellingMeals = mealSalesCount.entrySet().stream()
            .sorted(Map.Entry.<Long, Integer>comparingByValue().reversed())
            .limit(10)
            .map(entry -> {
                Meal meal = mealRepository.findById(entry.getKey()).orElse(null);
                Map<String, Object> mealInfo = new HashMap<>();
                if (meal != null) {
                    mealInfo.put("mealId", meal.getId());
                    mealInfo.put("mealName", meal.getName());
                    mealInfo.put("salesCount", entry.getValue());
                    mealInfo.put("revenue", mealSalesRevenue.get(entry.getKey()));
                }
                return mealInfo;
            })
            .collect(Collectors.toList());
        
        analysis.put("topSellingMeals", topSellingMeals);
        
        // 分类销售分析
        Map<String, Integer> categorySales = getCategorySalesAnalysis(completedOrders);
        analysis.put("categorySales", categorySales);
        
        // 商家销售排行
        Map<String, Object> merchantSales = getMerchantSalesAnalysis(completedOrders);
        analysis.put("merchantSales", merchantSales);
        
        return analysis;
    }
    
    /**
     * 获取推荐效果分析
     */
    @Cacheable(value = "analytics", key = "'recommendation_performance_' + #days")
    public Map<String, Object> getRecommendationPerformance(int days) {
        Map<String, Object> performance = new HashMap<>();
        
        // 这里可以集成推荐系统的日志数据
        // 由于推荐日志存储在Redis中，这里提供一个框架
        
        // 推荐算法性能对比
        Map<String, Map<String, Object>> algorithmPerformance = new HashMap<>();
        
        String[] algorithms = {"personalized", "collaborative", "content_based", "hybrid", "popular"};
        for (String algorithm : algorithms) {
            Map<String, Object> metrics = new HashMap<>();
            // 这里应该从推荐日志中获取实际数据
            metrics.put("totalRequests", 0);
            metrics.put("averageResponseTime", 0.0);
            metrics.put("clickThroughRate", 0.0);
            metrics.put("conversionRate", 0.0);
            algorithmPerformance.put(algorithm, metrics);
        }
        
        performance.put("algorithmPerformance", algorithmPerformance);
        
        // 推荐覆盖率
        long totalMeals = mealRepository.count();
        performance.put("totalMeals", totalMeals);
        performance.put("recommendedMeals", 0); // 需要从推荐日志计算
        performance.put("coverageRate", 0.0);
        
        return performance;
    }
    
    /**
     * 获取实时数据仪表板
     */
    public Map<String, Object> getRealTimeDashboard() {
        Map<String, Object> dashboard = new HashMap<>();
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime todayStart = now.withHour(0).withMinute(0).withSecond(0);
        
        // 今日实时数据
        List<Order> todayOrders = orderRepository.findByCreatedAtBetween(todayStart, now);
        List<Order> todayCompletedOrders = todayOrders.stream()
            .filter(order -> order.getStatus() == OrderStatus.DELIVERED)
            .collect(Collectors.toList());
        
        dashboard.put("todayOrders", todayOrders.size());
        dashboard.put("todayCompletedOrders", todayCompletedOrders.size());
        
        BigDecimal todayRevenue = todayCompletedOrders.stream()
            .map(Order::getTotalAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        dashboard.put("todayRevenue", todayRevenue);
        
        // 在线用户数（简化实现）
        long onlineUsers = userRepository.findAll().stream()
            .filter(user -> user.getLastLoginAt() != null && 
                user.getLastLoginAt().isAfter(now.minusHours(1)))
            .count();
        dashboard.put("onlineUsers", onlineUsers);
        
        // 待处理订单
        long pendingOrders = orderRepository.countByStatus(OrderStatus.PENDING);
        dashboard.put("pendingOrders", pendingOrders);
        
        // 系统状态
        dashboard.put("systemStatus", "healthy");
        dashboard.put("lastUpdated", now);
        
        return dashboard;
    }
    
    // 辅助方法
    private Map<String, BigDecimal> getDailyRevenueTrend(List<Order> orders, int days) {
        Map<String, BigDecimal> trend = new LinkedHashMap<>();
        LocalDate endDate = LocalDate.now();
        
        for (int i = days - 1; i >= 0; i--) {
            LocalDate date = endDate.minusDays(i);
            String dateStr = date.format(DateTimeFormatter.ofPattern("MM-dd"));
            
            BigDecimal dayRevenue = orders.stream()
                .filter(order -> order.getCreatedAt().toLocalDate().equals(date))
                .map(Order::getTotalAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            trend.put(dateStr, dayRevenue);
        }
        
        return trend;
    }
    
    private Map<String, Long> getDailyOrderTrend(List<Order> orders, int days) {
        Map<String, Long> trend = new LinkedHashMap<>();
        LocalDate endDate = LocalDate.now();
        
        for (int i = days - 1; i >= 0; i--) {
            LocalDate date = endDate.minusDays(i);
            String dateStr = date.format(DateTimeFormatter.ofPattern("MM-dd"));
            
            long dayOrders = orders.stream()
                .filter(order -> order.getCreatedAt().toLocalDate().equals(date))
                .count();
            
            trend.put(dateStr, dayOrders);
        }
        
        return trend;
    }
    
    private Map<String, Long> getUserRegistrationTrend(int days) {
        Map<String, Long> trend = new LinkedHashMap<>();
        LocalDate endDate = LocalDate.now();
        List<User> allUsers = userRepository.findAll();
        
        for (int i = days - 1; i >= 0; i--) {
            LocalDate date = endDate.minusDays(i);
            String dateStr = date.format(DateTimeFormatter.ofPattern("MM-dd"));
            
            long dayRegistrations = allUsers.stream()
                .filter(user -> user.getCreatedAt().toLocalDate().equals(date))
                .count();
            
            trend.put(dateStr, dayRegistrations);
        }
        
        return trend;
    }
    
    private Map<String, Long> getUserOrderDistribution() {
        List<User> customers = userRepository.findByRole(UserRole.CUSTOMER);
        Map<String, Long> distribution = new LinkedHashMap<>();
        
        distribution.put("0订单", customers.stream()
            .filter(user -> orderRepository.countByCustomer(user) == 0)
            .count());
        distribution.put("1-5订单", customers.stream()
            .filter(user -> {
                long count = orderRepository.countByCustomer(user);
                return count >= 1 && count <= 5;
            })
            .count());
        distribution.put("6-10订单", customers.stream()
            .filter(user -> {
                long count = orderRepository.countByCustomer(user);
                return count >= 6 && count <= 10;
            })
            .count());
        distribution.put("10+订单", customers.stream()
            .filter(user -> orderRepository.countByCustomer(user) > 10)
            .count());
        
        return distribution;
    }
    
    private Map<String, Long> getRatingDistribution(List<Rating> ratings) {
        Map<String, Long> distribution = new LinkedHashMap<>();
        
        for (int i = 1; i <= 5; i++) {
            final int score = i;
            long count = ratings.stream()
                .filter(rating -> rating.getScore().intValue() == score)
                .count();
            distribution.put(score + "星", count);
        }
        
        return distribution;
    }
    
    private Map<String, Integer> getCategorySalesAnalysis(List<Order> orders) {
        Map<String, Integer> categorySales = new HashMap<>();
        
        for (Order order : orders) {
            for (OrderItem item : order.getOrderItems()) {
                String categoryName = item.getMeal().getCategory().getName();
                categorySales.merge(categoryName, item.getQuantity(), Integer::sum);
            }
        }
        
        return categorySales;
    }
    
    private Map<String, Object> getMerchantSalesAnalysis(List<Order> orders) {
        Map<Long, Integer> merchantOrderCount = new HashMap<>();
        Map<Long, BigDecimal> merchantRevenue = new HashMap<>();
        
        for (Order order : orders) {
            Long merchantId = order.getMerchant().getId();
            merchantOrderCount.merge(merchantId, 1, Integer::sum);
            merchantRevenue.merge(merchantId, order.getTotalAmount(), BigDecimal::add);
        }
        
        List<Map<String, Object>> topMerchants = merchantOrderCount.entrySet().stream()
            .sorted(Map.Entry.<Long, Integer>comparingByValue().reversed())
            .limit(10)
            .map(entry -> {
                Merchant merchant = merchantRepository.findById(entry.getKey()).orElse(null);
                Map<String, Object> merchantInfo = new HashMap<>();
                if (merchant != null) {
                    merchantInfo.put("merchantId", merchant.getId());
                    merchantInfo.put("merchantName", merchant.getName());
                    merchantInfo.put("orderCount", entry.getValue());
                    merchantInfo.put("revenue", merchantRevenue.get(entry.getKey()));
                }
                return merchantInfo;
            })
            .collect(Collectors.toList());
        
        Map<String, Object> result = new HashMap<>();
        result.put("topMerchants", topMerchants);
        result.put("totalMerchants", merchantOrderCount.size());
        
        return result;
    }
}
