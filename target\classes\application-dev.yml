spring:
  # Database Configuration for Development
  datasource:
    url: ******************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
  
  # JPA Configuration for Development
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: false

# Logging Configuration for Development
logging:
  level:
    com.foodorder: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
