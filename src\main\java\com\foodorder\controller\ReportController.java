package com.foodorder.controller;

import com.foodorder.service.ReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping("/reports")
@Tag(name = "数据报表", description = "数据报表下载相关接口")
public class ReportController {
    
    private static final Logger logger = LoggerFactory.getLogger(ReportController.class);
    
    @Autowired
    private ReportService reportService;
    
    @GetMapping("/sales/download")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MERCHANT')")
    @Operation(summary = "下载销售报表", description = "下载指定时间段的销售数据报表CSV文件")
    public ResponseEntity<ByteArrayResource> downloadSalesReport(
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") int days) {
        
        try {
            byte[] csvData = reportService.generateSalesReportCSV(days);
            ByteArrayResource resource = new ByteArrayResource(csvData);
            
            String filename = String.format("销售报表_%s.csv", 
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));
            
            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .contentType(MediaType.parseMediaType("text/csv"))
                .contentLength(csvData.length)
                .body(resource);
                
        } catch (IOException e) {
            logger.error("生成销售报表失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    @GetMapping("/meals/download")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MERCHANT')")
    @Operation(summary = "下载商品销售报表", description = "下载商品销售情况报表CSV文件")
    public ResponseEntity<ByteArrayResource> downloadMealSalesReport(
            @RequestParam(defaultValue = "30") int days) {
        
        try {
            byte[] csvData = reportService.generateMealSalesReportCSV(days);
            ByteArrayResource resource = new ByteArrayResource(csvData);
            
            String filename = String.format("商品销售报表_%s.csv", 
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));
            
            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .contentType(MediaType.parseMediaType("text/csv"))
                .contentLength(csvData.length)
                .body(resource);
                
        } catch (IOException e) {
            logger.error("生成商品销售报表失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    @GetMapping("/users/download")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "下载用户行为报表", description = "下载用户行为分析报表CSV文件")
    public ResponseEntity<ByteArrayResource> downloadUserBehaviorReport(
            @RequestParam(defaultValue = "30") int days) {
        
        try {
            byte[] csvData = reportService.generateUserBehaviorReportCSV(days);
            ByteArrayResource resource = new ByteArrayResource(csvData);
            
            String filename = String.format("用户行为报表_%s.csv", 
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));
            
            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .contentType(MediaType.parseMediaType("text/csv"))
                .contentLength(csvData.length)
                .body(resource);
                
        } catch (IOException e) {
            logger.error("生成用户行为报表失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    @GetMapping("/merchants/download")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "下载商家业绩报表", description = "下载商家业绩分析报表CSV文件")
    public ResponseEntity<ByteArrayResource> downloadMerchantPerformanceReport(
            @RequestParam(defaultValue = "30") int days) {
        
        try {
            byte[] csvData = reportService.generateMerchantPerformanceReportCSV(days);
            ByteArrayResource resource = new ByteArrayResource(csvData);
            
            String filename = String.format("商家业绩报表_%s.csv", 
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));
            
            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .contentType(MediaType.parseMediaType("text/csv"))
                .contentLength(csvData.length)
                .body(resource);
                
        } catch (IOException e) {
            logger.error("生成商家业绩报表失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    @GetMapping("/ratings/download")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MERCHANT')")
    @Operation(summary = "下载评分分析报表", description = "下载评分分析报表CSV文件")
    public ResponseEntity<ByteArrayResource> downloadRatingAnalysisReport(
            @RequestParam(defaultValue = "30") int days) {
        
        try {
            byte[] csvData = reportService.generateRatingAnalysisReportCSV(days);
            ByteArrayResource resource = new ByteArrayResource(csvData);
            
            String filename = String.format("评分分析报表_%s.csv", 
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));
            
            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .contentType(MediaType.parseMediaType("text/csv"))
                .contentLength(csvData.length)
                .body(resource);
                
        } catch (IOException e) {
            logger.error("生成评分分析报表失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    @GetMapping("/custom/download")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "下载自定义报表", description = "根据指定参数生成自定义报表")
    public ResponseEntity<ByteArrayResource> downloadCustomReport(
            @RequestParam String reportType,
            @RequestParam(defaultValue = "30") int days,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        try {
            byte[] csvData;
            String filename;
            
            switch (reportType.toLowerCase()) {
                case "sales":
                    csvData = reportService.generateSalesReportCSV(days);
                    filename = "自定义销售报表";
                    break;
                case "meals":
                    csvData = reportService.generateMealSalesReportCSV(days);
                    filename = "自定义商品报表";
                    break;
                case "users":
                    csvData = reportService.generateUserBehaviorReportCSV(days);
                    filename = "自定义用户报表";
                    break;
                case "merchants":
                    csvData = reportService.generateMerchantPerformanceReportCSV(days);
                    filename = "自定义商家报表";
                    break;
                case "ratings":
                    csvData = reportService.generateRatingAnalysisReportCSV(days);
                    filename = "自定义评分报表";
                    break;
                default:
                    return ResponseEntity.badRequest().build();
            }
            
            ByteArrayResource resource = new ByteArrayResource(csvData);
            String fullFilename = String.format("%s_%s.csv", 
                filename, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));
            
            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fullFilename + "\"")
                .contentType(MediaType.parseMediaType("text/csv"))
                .contentLength(csvData.length)
                .body(resource);
                
        } catch (IOException e) {
            logger.error("生成自定义报表失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
