package com.foodorder;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@SpringBootApplication
@EnableCaching
@EnableAsync
@EnableScheduling
@RestController
public class FoodOrderSystemApplication {

    public static void main(String[] args) {
        SpringApplication.run(FoodOrderSystemApplication.class, args);
    }

    @GetMapping("/")
    public String home() {
        return "Food Order System is running! Welcome to the application.";
    }

    @GetMapping("/health")
    public String health() {
        return "OK";
    }
}
