package com.foodorder.recommendation.impl;

import com.foodorder.entity.*;
import com.foodorder.recommendation.config.RecommendationConfig;
import com.foodorder.recommendation.logging.RecommendationLogger;
import com.foodorder.repository.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DefaultRecommendationEngineTest {
    
    @Mock
    private MealRepository mealRepository;
    
    @Mock
    private RatingRepository ratingRepository;
    
    @Mock
    private OrderRepository orderRepository;
    
    @Mock
    private CategoryRepository categoryRepository;
    
    @Mock
    private RecommendationConfig config;
    
    @Mock
    private RecommendationLogger recommendationLogger;
    
    @InjectMocks
    private DefaultRecommendationEngine recommendationEngine;
    
    private User user;
    private Category category;
    private Merchant merchant;
    private Meal meal1, meal2, meal3;
    private Rating rating1, rating2;
    
    @BeforeEach
    void setUp() {
        // 创建测试数据
        user = new User();
        user.setId(1L);
        user.setUsername("testuser");
        
        category = new Category();
        category.setId(1L);
        category.setName("中式菜品");
        
        merchant = new Merchant();
        merchant.setId(1L);
        merchant.setName("川味小厨");
        
        meal1 = new Meal();
        meal1.setId(1L);
        meal1.setName("麻婆豆腐");
        meal1.setPrice(BigDecimal.valueOf(28.00));
        meal1.setRating(BigDecimal.valueOf(4.5));
        meal1.setSalesCount(100);
        meal1.setCategory(category);
        meal1.setMerchant(merchant);
        meal1.setTags(Set.of("经典", "下饭"));
        
        meal2 = new Meal();
        meal2.setId(2L);
        meal2.setName("宫保鸡丁");
        meal2.setPrice(BigDecimal.valueOf(32.00));
        meal2.setRating(BigDecimal.valueOf(4.3));
        meal2.setSalesCount(80);
        meal2.setCategory(category);
        meal2.setMerchant(merchant);
        meal2.setTags(Set.of("经典", "辣"));
        
        meal3 = new Meal();
        meal3.setId(3L);
        meal3.setName("红烧肉");
        meal3.setPrice(BigDecimal.valueOf(35.00));
        meal3.setRating(BigDecimal.valueOf(4.7));
        meal3.setSalesCount(120);
        meal3.setCategory(category);
        meal3.setMerchant(merchant);
        meal3.setTags(Set.of("经典", "甜"));
        
        rating1 = new Rating();
        rating1.setId(1L);
        rating1.setUser(user);
        rating1.setMeal(meal1);
        rating1.setScore(BigDecimal.valueOf(5.0));
        rating1.setCreatedAt(LocalDateTime.now());
        
        rating2 = new Rating();
        rating2.setId(2L);
        rating2.setUser(user);
        rating2.setMeal(meal2);
        rating2.setScore(BigDecimal.valueOf(4.0));
        rating2.setCreatedAt(LocalDateTime.now());
        
        // 配置Mock
        RecommendationConfig.PersonalizedWeights personalizedWeights = new RecommendationConfig.PersonalizedWeights();
        personalizedWeights.setCategoryPreference(0.4);
        personalizedWeights.setTagPreference(0.3);
        personalizedWeights.setRatingWeight(0.2);
        personalizedWeights.setSalesWeight(0.1);
        
        RecommendationConfig.SimilarityWeights similarityWeights = new RecommendationConfig.SimilarityWeights();
        similarityWeights.setCategoryWeight(0.4);
        similarityWeights.setTagWeight(0.3);
        similarityWeights.setPriceWeight(0.2);
        similarityWeights.setRatingWeight(0.1);
        
        when(config.getPersonalizedWeights()).thenReturn(personalizedWeights);
        when(config.getSimilarityWeights()).thenReturn(similarityWeights);
    }
    
    @Test
    void testGetPersonalizedRecommendations_WithUserRatings() {
        // Given
        when(ratingRepository.findByUserOrderByCreatedAtDesc(user))
            .thenReturn(Arrays.asList(rating1, rating2));
        when(mealRepository.findAvailableMeals())
            .thenReturn(Arrays.asList(meal1, meal2, meal3));
        
        // When
        List<Meal> recommendations = recommendationEngine.getPersonalizedRecommendations(user, 5);
        
        // Then
        assertNotNull(recommendations);
        assertFalse(recommendations.isEmpty());
        verify(ratingRepository).findByUserOrderByCreatedAtDesc(user);
        verify(mealRepository).findAvailableMeals();
        verify(recommendationLogger).logRecommendationRequest(eq(user), eq("personalized"), eq(5), any(), anyLong());
    }
    
    @Test
    void testGetPersonalizedRecommendations_NewUser() {
        // Given
        when(ratingRepository.findByUserOrderByCreatedAtDesc(user))
            .thenReturn(Collections.emptyList());
        when(mealRepository.findPopularMeals(any(Pageable.class)))
            .thenReturn(Arrays.asList(meal3, meal1, meal2));
        
        // When
        List<Meal> recommendations = recommendationEngine.getPersonalizedRecommendations(user, 5);
        
        // Then
        assertNotNull(recommendations);
        assertEquals(3, recommendations.size());
        assertEquals(meal3, recommendations.get(0)); // 最高销量的商品
        verify(mealRepository).findPopularMeals(any(Pageable.class));
        verify(recommendationLogger).logRecommendationRequest(eq(user), eq("personalized_fallback_popular"), eq(5), any(), anyLong());
    }
    
    @Test
    void testGetSimilarMeals() {
        // Given
        when(mealRepository.findAvailableMeals())
            .thenReturn(Arrays.asList(meal1, meal2, meal3));
        
        // When
        List<Meal> similarMeals = recommendationEngine.getSimilarMeals(meal1, 3);
        
        // Then
        assertNotNull(similarMeals);
        assertFalse(similarMeals.isEmpty());
        assertFalse(similarMeals.contains(meal1)); // 不应包含自身
        verify(mealRepository).findAvailableMeals();
    }
    
    @Test
    void testGetPopularRecommendations() {
        // Given
        when(mealRepository.findPopularMeals(any(Pageable.class)))
            .thenReturn(Arrays.asList(meal3, meal1, meal2));
        
        // When
        List<Meal> popularMeals = recommendationEngine.getPopularRecommendations(5);
        
        // Then
        assertNotNull(popularMeals);
        assertEquals(3, popularMeals.size());
        assertEquals(meal3, popularMeals.get(0)); // 销量最高的商品应该排在第一位
        verify(mealRepository).findPopularMeals(PageRequest.of(0, 5));
    }
    
    @Test
    void testGetRealtimeRecommendations_WithRecentRatings() {
        // Given
        LocalDateTime since = LocalDateTime.now().minusDays(7);
        when(ratingRepository.findRecentRatingsByUser(user, since))
            .thenReturn(Arrays.asList(rating1));
        when(mealRepository.findAvailableMeals())
            .thenReturn(Arrays.asList(meal2, meal3));
        
        // When
        List<Meal> recommendations = recommendationEngine.getRealtimeRecommendations(user, 5);
        
        // Then
        assertNotNull(recommendations);
        verify(ratingRepository).findRecentRatingsByUser(eq(user), any(LocalDateTime.class));
    }
    
    @Test
    void testGetRealtimeRecommendations_NoRecentRatings() {
        // Given
        LocalDateTime since = LocalDateTime.now().minusDays(7);
        when(ratingRepository.findRecentRatingsByUser(user, since))
            .thenReturn(Collections.emptyList());
        when(ratingRepository.findByUserOrderByCreatedAtDesc(user))
            .thenReturn(Arrays.asList(rating1, rating2));
        when(mealRepository.findAvailableMeals())
            .thenReturn(Arrays.asList(meal1, meal2, meal3));
        
        // When
        List<Meal> recommendations = recommendationEngine.getRealtimeRecommendations(user, 5);
        
        // Then
        assertNotNull(recommendations);
        verify(ratingRepository).findRecentRatingsByUser(eq(user), any(LocalDateTime.class));
    }
    
    @Test
    void testGetCollaborativeFilteringRecommendations_WithUserRatings() {
        // Given
        when(ratingRepository.findByUser(user))
            .thenReturn(Arrays.asList(rating1, rating2));
        
        User similarUser = new User();
        similarUser.setId(2L);
        
        Rating similarUserRating = new Rating();
        similarUserRating.setUser(similarUser);
        similarUserRating.setMeal(meal3);
        similarUserRating.setScore(BigDecimal.valueOf(4.5));
        
        when(ratingRepository.findAll())
            .thenReturn(Arrays.asList(rating1, rating2, similarUserRating));
        when(ratingRepository.findByUser(similarUser))
            .thenReturn(Arrays.asList(similarUserRating));
        when(mealRepository.findById(3L))
            .thenReturn(Optional.of(meal3));
        
        // When
        List<Meal> recommendations = recommendationEngine.getCollaborativeFilteringRecommendations(user, 5);
        
        // Then
        assertNotNull(recommendations);
        verify(ratingRepository).findByUser(user);
    }
    
    @Test
    void testGetContentBasedRecommendations() {
        // Given
        when(ratingRepository.findByUser(user))
            .thenReturn(Arrays.asList(rating1, rating2));
        when(mealRepository.findAvailableMeals())
            .thenReturn(Arrays.asList(meal1, meal2, meal3));
        
        // When
        List<Meal> recommendations = recommendationEngine.getContentBasedRecommendations(user, 5);
        
        // Then
        assertNotNull(recommendations);
        verify(ratingRepository).findByUser(user);
        verify(mealRepository).findAvailableMeals();
    }
    
    @Test
    void testGetHybridRecommendations() {
        // Given
        when(ratingRepository.findByUserOrderByCreatedAtDesc(user))
            .thenReturn(Arrays.asList(rating1, rating2));
        when(ratingRepository.findByUser(user))
            .thenReturn(Arrays.asList(rating1, rating2));
        when(mealRepository.findAvailableMeals())
            .thenReturn(Arrays.asList(meal1, meal2, meal3));
        when(mealRepository.findPopularMeals(any(Pageable.class)))
            .thenReturn(Arrays.asList(meal3, meal1, meal2));
        
        // When
        List<Meal> recommendations = recommendationEngine.getHybridRecommendations(user, 10);
        
        // Then
        assertNotNull(recommendations);
        assertTrue(recommendations.size() <= 10);
    }
}
