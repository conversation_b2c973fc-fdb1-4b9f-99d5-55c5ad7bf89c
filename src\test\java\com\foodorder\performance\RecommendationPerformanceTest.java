package com.foodorder.performance;

import com.foodorder.entity.*;
import com.foodorder.recommendation.RecommendationEngine;
import com.foodorder.repository.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
class RecommendationPerformanceTest {
    
    @Autowired
    private RecommendationEngine recommendationEngine;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private MealRepository mealRepository;
    
    @Autowired
    private CategoryRepository categoryRepository;
    
    @Autowired
    private MerchantRepository merchantRepository;
    
    @Autowired
    private RatingRepository ratingRepository;
    
    private List<User> testUsers;
    private List<Meal> testMeals;
    
    @BeforeEach
    void setUp() {
        // 创建测试数据
        createTestData();
    }
    
    private void createTestData() {
        // 创建分类
        Category category1 = new Category();
        category1.setName("中式菜品");
        category1 = categoryRepository.save(category1);
        
        Category category2 = new Category();
        category2.setName("西式菜品");
        category2 = categoryRepository.save(category2);
        
        // 创建商家
        Merchant merchant1 = new Merchant();
        merchant1.setName("川味小厨");
        merchant1.setStatus(MerchantStatus.APPROVED);
        merchant1 = merchantRepository.save(merchant1);
        
        Merchant merchant2 = new Merchant();
        merchant2.setName("意式风情");
        merchant2.setStatus(MerchantStatus.APPROVED);
        merchant2 = merchantRepository.save(merchant2);
        
        // 创建用户
        testUsers = new ArrayList<>();
        for (int i = 1; i <= 100; i++) {
            User user = new User();
            user.setUsername("user" + i);
            user.setEmail("user" + i + "@test.com");
            user.setPassword("password");
            user.setRoles(Set.of(UserRole.CUSTOMER));
            user.setEnabled(true);
            testUsers.add(userRepository.save(user));
        }
        
        // 创建商品
        testMeals = new ArrayList<>();
        Random random = new Random();
        
        for (int i = 1; i <= 500; i++) {
            Meal meal = new Meal();
            meal.setName("商品" + i);
            meal.setDescription("商品描述" + i);
            meal.setPrice(BigDecimal.valueOf(20 + random.nextInt(80)));
            meal.setRating(BigDecimal.valueOf(3.0 + random.nextDouble() * 2.0));
            meal.setSalesCount(random.nextInt(200));
            meal.setCategory(i % 2 == 0 ? category1 : category2);
            meal.setMerchant(i % 2 == 0 ? merchant1 : merchant2);
            meal.setStatus(MealStatus.AVAILABLE);
            meal.setTags(Set.of("标签" + (i % 5), "特色"));
            testMeals.add(mealRepository.save(meal));
        }
        
        // 创建评分数据
        for (User user : testUsers.subList(0, 50)) { // 只为前50个用户创建评分
            for (int i = 0; i < 10; i++) { // 每个用户评分10个商品
                Meal meal = testMeals.get(random.nextInt(testMeals.size()));
                
                Rating rating = new Rating();
                rating.setUser(user);
                rating.setMeal(meal);
                rating.setScore(BigDecimal.valueOf(1 + random.nextInt(5)));
                rating.setReview("评价内容" + i);
                rating.setCreatedAt(LocalDateTime.now().minusDays(random.nextInt(30)));
                ratingRepository.save(rating);
            }
        }
    }
    
    @Test
    void testPersonalizedRecommendationPerformance() {
        // 测试个性化推荐的性能
        User testUser = testUsers.get(0);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < 10; i++) {
            List<Meal> recommendations = recommendationEngine.getPersonalizedRecommendations(testUser, 10);
            assertNotNull(recommendations);
            assertTrue(recommendations.size() <= 10);
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        System.out.println("个性化推荐性能测试:");
        System.out.println("10次推荐总耗时: " + totalTime + "ms");
        System.out.println("平均每次推荐耗时: " + (totalTime / 10.0) + "ms");
        
        // 断言平均响应时间应该小于500ms
        assertTrue(totalTime / 10.0 < 500, "个性化推荐平均响应时间应该小于500ms");
    }
    
    @Test
    void testPopularRecommendationPerformance() {
        // 测试热门推荐的性能
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < 50; i++) {
            List<Meal> recommendations = recommendationEngine.getPopularRecommendations(10);
            assertNotNull(recommendations);
            assertTrue(recommendations.size() <= 10);
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        System.out.println("热门推荐性能测试:");
        System.out.println("50次推荐总耗时: " + totalTime + "ms");
        System.out.println("平均每次推荐耗时: " + (totalTime / 50.0) + "ms");
        
        // 断言平均响应时间应该小于100ms（热门推荐应该更快）
        assertTrue(totalTime / 50.0 < 100, "热门推荐平均响应时间应该小于100ms");
    }
    
    @Test
    void testSimilarMealRecommendationPerformance() {
        // 测试相似商品推荐的性能
        Meal baseMeal = testMeals.get(0);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < 20; i++) {
            List<Meal> recommendations = recommendationEngine.getSimilarMeals(baseMeal, 5);
            assertNotNull(recommendations);
            assertTrue(recommendations.size() <= 5);
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        System.out.println("相似商品推荐性能测试:");
        System.out.println("20次推荐总耗时: " + totalTime + "ms");
        System.out.println("平均每次推荐耗时: " + (totalTime / 20.0) + "ms");
        
        // 断言平均响应时间应该小于300ms
        assertTrue(totalTime / 20.0 < 300, "相似商品推荐平均响应时间应该小于300ms");
    }
    
    @Test
    void testConcurrentRecommendationPerformance() throws InterruptedException {
        // 测试并发推荐的性能
        ExecutorService executor = Executors.newFixedThreadPool(10);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        long startTime = System.currentTimeMillis();
        
        // 创建100个并发推荐请求
        for (int i = 0; i < 100; i++) {
            final int userIndex = i % testUsers.size();
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                User user = testUsers.get(userIndex);
                List<Meal> recommendations = recommendationEngine.getPersonalizedRecommendations(user, 10);
                assertNotNull(recommendations);
            }, executor);
            futures.add(future);
        }
        
        // 等待所有请求完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        System.out.println("并发推荐性能测试:");
        System.out.println("100个并发请求总耗时: " + totalTime + "ms");
        System.out.println("平均每个请求耗时: " + (totalTime / 100.0) + "ms");
        
        executor.shutdown();
        executor.awaitTermination(10, TimeUnit.SECONDS);
        
        // 断言总耗时应该小于10秒
        assertTrue(totalTime < 10000, "100个并发推荐请求应该在10秒内完成");
    }
    
    @Test
    void testRecommendationAccuracy() {
        // 测试推荐准确性
        User testUser = testUsers.get(0);
        
        // 获取用户的历史评分
        List<Rating> userRatings = ratingRepository.findByUser(testUser);
        
        if (!userRatings.isEmpty()) {
            // 获取个性化推荐
            List<Meal> recommendations = recommendationEngine.getPersonalizedRecommendations(testUser, 20);
            
            // 分析推荐质量
            double avgRecommendedRating = recommendations.stream()
                .mapToDouble(meal -> meal.getRating().doubleValue())
                .average()
                .orElse(0.0);
            
            double avgUserRating = userRatings.stream()
                .mapToDouble(rating -> rating.getScore().doubleValue())
                .average()
                .orElse(0.0);
            
            System.out.println("推荐准确性测试:");
            System.out.println("推荐商品平均评分: " + avgRecommendedRating);
            System.out.println("用户历史平均评分: " + avgUserRating);
            
            // 推荐的商品平均评分应该不低于用户历史平均评分
            assertTrue(avgRecommendedRating >= avgUserRating - 0.5, 
                "推荐商品的平均评分应该接近或高于用户历史平均评分");
        }
    }
    
    @Test
    void testRecommendationDiversity() {
        // 测试推荐多样性
        User testUser = testUsers.get(0);
        List<Meal> recommendations = recommendationEngine.getPersonalizedRecommendations(testUser, 20);
        
        // 计算分类多样性
        Set<Long> categoryIds = recommendations.stream()
            .map(meal -> meal.getCategory().getId())
            .collect(HashSet::new, Set::add, Set::addAll);
        
        double categoryDiversity = (double) categoryIds.size() / recommendations.size();
        
        // 计算商家多样性
        Set<Long> merchantIds = recommendations.stream()
            .map(meal -> meal.getMerchant().getId())
            .collect(HashSet::new, Set::add, Set::addAll);
        
        double merchantDiversity = (double) merchantIds.size() / recommendations.size();
        
        System.out.println("推荐多样性测试:");
        System.out.println("分类多样性: " + categoryDiversity);
        System.out.println("商家多样性: " + merchantDiversity);
        
        // 多样性应该大于0.3（即至少30%的多样性）
        assertTrue(categoryDiversity > 0.3, "推荐应该具有足够的分类多样性");
        assertTrue(merchantDiversity > 0.3, "推荐应该具有足够的商家多样性");
    }
}
