package com.foodorder.repository;

import com.foodorder.entity.Category;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CategoryRepository extends JpaRepository<Category, Long> {
    
    List<Category> findByParentIsNull();
    
    List<Category> findByParentIsNotNull();
    
    List<Category> findByParent(Category parent);
    
    List<Category> findByParentId(Long parentId);
    
    List<Category> findByEnabledTrue();
    
    List<Category> findByParentIsNullAndEnabledTrueOrderBySortOrder();
    
    List<Category> findByParentAndEnabledTrueOrderBySortOrder(Category parent);
    
    @Query("SELECT c FROM Category c WHERE c.parent IS NULL AND c.enabled = true ORDER BY c.sortOrder")
    List<Category> findRootCategories();
    
    @Query("SELECT c FROM Category c WHERE c.parent.id = :parentId AND c.enabled = true ORDER BY c.sortOrder")
    List<Category> findSubCategories(Long parentId);
    
    boolean existsByName(String name);
    
    boolean existsByNameAndParent(String name, Category parent);
}
