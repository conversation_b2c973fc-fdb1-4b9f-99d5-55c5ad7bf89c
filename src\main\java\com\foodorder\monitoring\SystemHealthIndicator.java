package com.foodorder.monitoring;

import com.foodorder.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuator.health.Health;
import org.springframework.boot.actuator.health.HealthIndicator;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

@Component
public class SystemHealthIndicator implements HealthIndicator {
    
    @Autowired
    private DataSource dataSource;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private UserRepository userRepository;
    
    @Override
    public Health health() {
        Health.Builder builder = new Health.Builder();
        
        try {
            // 检查数据库连接
            checkDatabase(builder);
            
            // 检查Redis连接
            checkRedis(builder);
            
            // 检查业务功能
            checkBusinessLogic(builder);
            
            builder.up();
            
        } catch (Exception e) {
            builder.down()
                .withDetail("error", e.getMessage());
        }
        
        return builder.build();
    }
    
    private void checkDatabase(Health.Builder builder) throws SQLException {
        try (Connection connection = dataSource.getConnection()) {
            if (connection.isValid(5)) {
                builder.withDetail("database", "UP");
                
                // 检查用户表
                long userCount = userRepository.count();
                builder.withDetail("userCount", userCount);
            } else {
                throw new SQLException("数据库连接无效");
            }
        }
    }
    
    private void checkRedis(Health.Builder builder) {
        try {
            redisTemplate.opsForValue().set("health_check", "test");
            String value = (String) redisTemplate.opsForValue().get("health_check");
            
            if ("test".equals(value)) {
                builder.withDetail("redis", "UP");
                redisTemplate.delete("health_check");
            } else {
                throw new RuntimeException("Redis读写测试失败");
            }
        } catch (Exception e) {
            builder.withDetail("redis", "DOWN")
                .withDetail("redisError", e.getMessage());
            throw new RuntimeException("Redis连接失败", e);
        }
    }
    
    private void checkBusinessLogic(Health.Builder builder) {
        try {
            // 检查关键业务功能
            long userCount = userRepository.count();
            builder.withDetail("businessLogic", "UP")
                .withDetail("totalUsers", userCount);
            
        } catch (Exception e) {
            builder.withDetail("businessLogic", "DOWN")
                .withDetail("businessError", e.getMessage());
            throw new RuntimeException("业务逻辑检查失败", e);
        }
    }
}
