package com.foodorder.dto.category;

import com.foodorder.entity.Category;

import java.util.List;
import java.util.stream.Collectors;

public class CategoryDto {
    
    private Long id;
    private String name;
    private String description;
    private String iconUrl;
    private Integer sortOrder;
    private Boolean enabled;
    private Long parentId;
    private String parentName;
    private List<CategoryDto> children;
    
    public CategoryDto() {}
    
    public CategoryDto(Category category) {
        this.id = category.getId();
        this.name = category.getName();
        this.description = category.getDescription();
        this.iconUrl = category.getIconUrl();
        this.sortOrder = category.getSortOrder();
        this.enabled = category.getEnabled();
        
        if (category.getParent() != null) {
            this.parentId = category.getParent().getId();
            this.parentName = category.getParent().getName();
        }
        
        if (category.getChildren() != null && !category.getChildren().isEmpty()) {
            this.children = category.getChildren().stream()
                .filter(child -> child.getEnabled())
                .map(CategoryDto::new)
                .collect(Collectors.toList());
        }
    }
    
    public static CategoryDto fromCategory(Category category) {
        return new CategoryDto(category);
    }
    
    public static CategoryDto fromCategoryWithoutChildren(Category category) {
        CategoryDto dto = new CategoryDto();
        dto.setId(category.getId());
        dto.setName(category.getName());
        dto.setDescription(category.getDescription());
        dto.setIconUrl(category.getIconUrl());
        dto.setSortOrder(category.getSortOrder());
        dto.setEnabled(category.getEnabled());
        
        if (category.getParent() != null) {
            dto.setParentId(category.getParent().getId());
            dto.setParentName(category.getParent().getName());
        }
        
        return dto;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getIconUrl() {
        return iconUrl;
    }
    
    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    public Boolean getEnabled() {
        return enabled;
    }
    
    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
    
    public Long getParentId() {
        return parentId;
    }
    
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }
    
    public String getParentName() {
        return parentName;
    }
    
    public void setParentName(String parentName) {
        this.parentName = parentName;
    }
    
    public List<CategoryDto> getChildren() {
        return children;
    }
    
    public void setChildren(List<CategoryDto> children) {
        this.children = children;
    }
}
