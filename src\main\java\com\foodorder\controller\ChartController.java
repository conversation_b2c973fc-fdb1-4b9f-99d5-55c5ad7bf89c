package com.foodorder.controller;

import com.foodorder.common.response.ApiResponse;
import com.foodorder.dto.analytics.ChartDataDto;
import com.foodorder.service.ChartDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/charts")
@Tag(name = "图表数据", description = "数据可视化图表相关接口")
public class ChartController {
    
    @Autowired
    private ChartDataService chartDataService;
    
    @GetMapping("/revenue-trend")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MERCHANT')")
    @Operation(summary = "获取收入趋势图", description = "获取收入趋势图表数据")
    public ResponseEntity<ApiResponse<ChartDataDto>> getRevenueTrendChart(
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "7") int days) {
        
        ChartDataDto chartData = chartDataService.getRevenueTrendChart(days);
        return ResponseEntity.ok(ApiResponse.success(chartData));
    }
    
    @GetMapping("/order-trend")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MERCHANT')")
    @Operation(summary = "获取订单趋势图", description = "获取订单趋势图表数据")
    public ResponseEntity<ApiResponse<ChartDataDto>> getOrderTrendChart(
            @RequestParam(defaultValue = "7") int days) {
        
        ChartDataDto chartData = chartDataService.getOrderTrendChart(days);
        return ResponseEntity.ok(ApiResponse.success(chartData));
    }
    
    @GetMapping("/category-sales")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MERCHANT')")
    @Operation(summary = "获取分类销售饼图", description = "获取商品分类销售分布饼图数据")
    public ResponseEntity<ApiResponse<ChartDataDto>> getCategorySalesChart(
            @RequestParam(defaultValue = "30") int days) {
        
        ChartDataDto chartData = chartDataService.getCategorySalesChart(days);
        return ResponseEntity.ok(ApiResponse.success(chartData));
    }
    
    @GetMapping("/user-registration")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "获取用户注册趋势图", description = "获取用户注册趋势图表数据")
    public ResponseEntity<ApiResponse<ChartDataDto>> getUserRegistrationChart(
            @RequestParam(defaultValue = "30") int days) {
        
        ChartDataDto chartData = chartDataService.getUserRegistrationChart(days);
        return ResponseEntity.ok(ApiResponse.success(chartData));
    }
    
    @GetMapping("/rating-distribution")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MERCHANT')")
    @Operation(summary = "获取评分分布图", description = "获取用户评分分布图表数据")
    public ResponseEntity<ApiResponse<ChartDataDto>> getRatingDistributionChart(
            @RequestParam(defaultValue = "30") int days) {
        
        ChartDataDto chartData = chartDataService.getRatingDistributionChart(days);
        return ResponseEntity.ok(ApiResponse.success(chartData));
    }
    
    @GetMapping("/dashboard")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "获取仪表板图表集合", description = "获取管理员仪表板所有图表数据")
    public ResponseEntity<ApiResponse<Map<String, ChartDataDto>>> getDashboardCharts() {
        Map<String, ChartDataDto> charts = chartDataService.getDashboardCharts();
        return ResponseEntity.ok(ApiResponse.success(charts));
    }
    
    @GetMapping("/merchant/{merchantId}")
    @PreAuthorize("hasRole('ADMIN') or (hasRole('MERCHANT') and @authService.getCurrentUser().merchant.id == #merchantId)")
    @Operation(summary = "获取商家专用图表", description = "获取指定商家的专用图表数据")
    public ResponseEntity<ApiResponse<Map<String, ChartDataDto>>> getMerchantCharts(
            @PathVariable Long merchantId) {
        
        Map<String, ChartDataDto> charts = chartDataService.getMerchantCharts(merchantId);
        return ResponseEntity.ok(ApiResponse.success(charts));
    }
}
