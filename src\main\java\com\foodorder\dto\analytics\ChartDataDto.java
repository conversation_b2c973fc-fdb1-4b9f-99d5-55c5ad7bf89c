package com.foodorder.dto.analytics;

import java.util.List;
import java.util.Map;

/**
 * 图表数据传输对象
 */
public class ChartDataDto {
    
    private String title;
    private String type; // line, bar, pie, doughnut, area
    private List<String> labels;
    private List<DatasetDto> datasets;
    private Map<String, Object> options;
    
    public ChartDataDto() {}
    
    public ChartDataDto(String title, String type, List<String> labels, List<DatasetDto> datasets) {
        this.title = title;
        this.type = type;
        this.labels = labels;
        this.datasets = datasets;
    }
    
    // Getters and Setters
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public List<String> getLabels() {
        return labels;
    }
    
    public void setLabels(List<String> labels) {
        this.labels = labels;
    }
    
    public List<DatasetDto> getDatasets() {
        return datasets;
    }
    
    public void setDatasets(List<DatasetDto> datasets) {
        this.datasets = datasets;
    }
    
    public Map<String, Object> getOptions() {
        return options;
    }
    
    public void setOptions(Map<String, Object> options) {
        this.options = options;
    }
    
    /**
     * 数据集DTO
     */
    public static class DatasetDto {
        private String label;
        private List<Object> data;
        private String backgroundColor;
        private String borderColor;
        private Integer borderWidth;
        private Boolean fill;
        private String tension;
        
        public DatasetDto() {}
        
        public DatasetDto(String label, List<Object> data) {
            this.label = label;
            this.data = data;
        }
        
        public DatasetDto(String label, List<Object> data, String backgroundColor, String borderColor) {
            this.label = label;
            this.data = data;
            this.backgroundColor = backgroundColor;
            this.borderColor = borderColor;
        }
        
        // Getters and Setters
        public String getLabel() {
            return label;
        }
        
        public void setLabel(String label) {
            this.label = label;
        }
        
        public List<Object> getData() {
            return data;
        }
        
        public void setData(List<Object> data) {
            this.data = data;
        }
        
        public String getBackgroundColor() {
            return backgroundColor;
        }
        
        public void setBackgroundColor(String backgroundColor) {
            this.backgroundColor = backgroundColor;
        }
        
        public String getBorderColor() {
            return borderColor;
        }
        
        public void setBorderColor(String borderColor) {
            this.borderColor = borderColor;
        }
        
        public Integer getBorderWidth() {
            return borderWidth;
        }
        
        public void setBorderWidth(Integer borderWidth) {
            this.borderWidth = borderWidth;
        }
        
        public Boolean getFill() {
            return fill;
        }
        
        public void setFill(Boolean fill) {
            this.fill = fill;
        }
        
        public String getTension() {
            return tension;
        }
        
        public void setTension(String tension) {
            this.tension = tension;
        }
    }
}
