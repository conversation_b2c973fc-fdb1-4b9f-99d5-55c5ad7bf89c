// 全局变量
let currentUser = null;
let authToken = localStorage.getItem('authToken');

// API基础URL
const API_BASE_URL = '/api';

// 工具函数
const utils = {
    // 显示提示消息
    showToast: function(message, type = 'info') {
        const toastContainer = document.getElementById('toastContainer') || this.createToastContainer();
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        toastContainer.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // 自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    },
    
    // 创建提示容器
    createToastContainer: function() {
        const container = document.createElement('div');
        container.id = 'toastContainer';
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
        return container;
    },
    
    // 格式化价格
    formatPrice: function(price) {
        return `¥${parseFloat(price).toFixed(2)}`;
    },
    
    // 格式化日期
    formatDate: function(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN');
    },
    
    // 生成星级评分HTML
    generateStars: function(rating, maxStars = 5) {
        let starsHtml = '';
        for (let i = 1; i <= maxStars; i++) {
            if (i <= rating) {
                starsHtml += '<i class="fas fa-star text-warning"></i>';
            } else if (i - 0.5 <= rating) {
                starsHtml += '<i class="fas fa-star-half-alt text-warning"></i>';
            } else {
                starsHtml += '<i class="far fa-star text-warning"></i>';
            }
        }
        return starsHtml;
    }
};

// API请求函数
const api = {
    // 通用请求方法
    request: async function(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            }
        };
        
        if (authToken) {
            defaultOptions.headers['Authorization'] = `Bearer ${authToken}`;
        }
        
        const finalOptions = { ...defaultOptions, ...options };
        if (finalOptions.body && typeof finalOptions.body === 'object') {
            finalOptions.body = JSON.stringify(finalOptions.body);
        }
        
        try {
            const response = await fetch(API_BASE_URL + url, finalOptions);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || '请求失败');
            }
            
            return data;
        } catch (error) {
            console.error('API请求错误:', error);
            throw error;
        }
    },
    
    // 用户认证
    auth: {
        login: function(credentials) {
            return api.request('/auth/login', {
                method: 'POST',
                body: credentials
            });
        },
        
        register: function(userData) {
            return api.request('/auth/register', {
                method: 'POST',
                body: userData
            });
        },
        
        logout: function() {
            authToken = null;
            localStorage.removeItem('authToken');
            currentUser = null;
        }
    },
    
    // 餐饮相关
    meals: {
        getAll: function(page = 0, size = 20) {
            return api.request(`/meals?page=${page}&size=${size}`);
        },
        
        getById: function(id) {
            return api.request(`/meals/${id}`);
        },
        
        search: function(searchData) {
            return api.request('/meals/search', {
                method: 'POST',
                body: searchData
            });
        }
    },
    
    // 推荐相关
    recommendations: {
        getHomepage: function(limit = 10) {
            return api.request(`/recommendations/homepage?limit=${limit}`);
        },
        
        getPersonalized: function(limit = 10) {
            return api.request(`/recommendations/personalized?limit=${limit}`);
        },
        
        getSimilar: function(mealId, limit = 5) {
            return api.request(`/recommendations/similar/${mealId}?limit=${limit}`);
        }
    },
    
    // 分类相关
    categories: {
        getTree: function() {
            return api.request('/categories/tree');
        }
    }
};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // 检查用户登录状态
    if (authToken) {
        // 可以在这里验证token有效性
        updateUIForLoggedInUser();
    }
    
    // 绑定事件监听器
    bindEventListeners();
    
    // 加载首页数据
    loadHomepageData();
}

function bindEventListeners() {
    // 登录按钮
    document.getElementById('loginBtn').addEventListener('click', function() {
        const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
        loginModal.show();
    });
    
    // 注册按钮
    document.getElementById('registerBtn').addEventListener('click', function() {
        const registerModal = new bootstrap.Modal(document.getElementById('registerModal'));
        registerModal.show();
    });
    
    // 登录表单提交
    document.getElementById('loginSubmit').addEventListener('click', handleLogin);
    
    // 注册表单提交
    document.getElementById('registerSubmit').addEventListener('click', handleRegister);
}

async function handleLogin() {
    const username = document.getElementById('loginUsername').value;
    const password = document.getElementById('loginPassword').value;
    
    if (!username || !password) {
        utils.showToast('请填写用户名和密码', 'warning');
        return;
    }
    
    try {
        const response = await api.auth.login({
            usernameOrEmail: username,
            password: password
        });
        
        if (response.success) {
            authToken = response.data.token;
            localStorage.setItem('authToken', authToken);
            currentUser = response.data;
            
            utils.showToast('登录成功！', 'success');
            bootstrap.Modal.getInstance(document.getElementById('loginModal')).hide();
            updateUIForLoggedInUser();
        }
    } catch (error) {
        utils.showToast(error.message || '登录失败', 'danger');
    }
}

async function handleRegister() {
    const username = document.getElementById('registerUsername').value;
    const email = document.getElementById('registerEmail').value;
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const realName = document.getElementById('realName').value;
    
    if (!username || !email || !password) {
        utils.showToast('请填写必填字段', 'warning');
        return;
    }
    
    if (password !== confirmPassword) {
        utils.showToast('两次输入的密码不一致', 'warning');
        return;
    }
    
    try {
        const response = await api.auth.register({
            username: username,
            email: email,
            password: password,
            confirmPassword: confirmPassword,
            realName: realName
        });
        
        if (response.success) {
            utils.showToast('注册成功！请登录', 'success');
            bootstrap.Modal.getInstance(document.getElementById('registerModal')).hide();
            
            // 清空表单
            document.getElementById('registerForm').reset();
        }
    } catch (error) {
        utils.showToast(error.message || '注册失败', 'danger');
    }
}

function updateUIForLoggedInUser() {
    // 更新导航栏
    const loginBtn = document.getElementById('loginBtn');
    const registerBtn = document.getElementById('registerBtn');
    
    if (currentUser) {
        loginBtn.innerHTML = `<i class="fas fa-user me-1"></i>${currentUser.username}`;
        loginBtn.href = '#profile';
        registerBtn.innerHTML = '<i class="fas fa-sign-out-alt me-1"></i>退出';
        registerBtn.addEventListener('click', handleLogout);
    }
}

function handleLogout() {
    api.auth.logout();
    utils.showToast('已退出登录', 'info');
    location.reload();
}

async function loadHomepageData() {
    try {
        // 加载推荐数据
        await loadRecommendations();
        
        // 加载分类数据
        await loadCategories();
        
        // 加载热门商家（模拟数据）
        loadMerchants();
        
    } catch (error) {
        console.error('加载首页数据失败:', error);
    }
}

async function loadRecommendations() {
    try {
        const response = await api.recommendations.getHomepage(8);
        if (response.success && response.data) {
            renderRecommendations(response.data);
        }
    } catch (error) {
        console.error('加载推荐数据失败:', error);
        // 显示默认内容或错误信息
        renderDefaultRecommendations();
    }
}

function renderRecommendations(meals) {
    const container = document.getElementById('recommendationCards');
    container.innerHTML = '';
    
    meals.forEach(meal => {
        const card = createMealCard(meal);
        container.appendChild(card);
    });
}

function renderDefaultRecommendations() {
    const container = document.getElementById('recommendationCards');
    container.innerHTML = `
        <div class="col-12 text-center">
            <p class="text-muted">暂无推荐数据，请稍后再试</p>
        </div>
    `;
}

function createMealCard(meal) {
    const col = document.createElement('div');
    col.className = 'col-lg-3 col-md-4 col-sm-6 mb-4';
    
    col.innerHTML = `
        <div class="card meal-card h-100">
            <img src="${meal.imageUrl || '/images/default-meal.jpg'}" class="card-img-top" alt="${meal.name}">
            <div class="card-body">
                <h6 class="card-title">${meal.name}</h6>
                <p class="card-text text-muted small">${meal.description || ''}</p>
                <div class="d-flex justify-content-between align-items-center">
                    <span class="meal-price">${utils.formatPrice(meal.price)}</span>
                    <div class="meal-rating">
                        ${utils.generateStars(meal.rating)}
                        <small class="text-muted">(${meal.ratingCount || 0})</small>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent">
                <button class="btn btn-primary btn-sm w-100" onclick="addToCart(${meal.id})">
                    <i class="fas fa-cart-plus me-1"></i>加入购物车
                </button>
            </div>
        </div>
    `;
    
    return col;
}

async function loadCategories() {
    try {
        const response = await api.categories.getTree();
        if (response.success && response.data) {
            renderCategories(response.data);
        }
    } catch (error) {
        console.error('加载分类数据失败:', error);
        renderDefaultCategories();
    }
}

function renderCategories(categories) {
    const container = document.getElementById('categoryCards');
    container.innerHTML = '';
    
    categories.slice(0, 6).forEach(category => {
        const card = createCategoryCard(category);
        container.appendChild(card);
    });
}

function renderDefaultCategories() {
    const defaultCategories = [
        { id: 1, name: '中式菜品', iconUrl: 'fas fa-bowl-rice' },
        { id: 2, name: '西式菜品', iconUrl: 'fas fa-hamburger' },
        { id: 3, name: '日韩料理', iconUrl: 'fas fa-fish' },
        { id: 4, name: '饮品', iconUrl: 'fas fa-coffee' },
        { id: 5, name: '甜品', iconUrl: 'fas fa-ice-cream' },
        { id: 6, name: '小食', iconUrl: 'fas fa-cookie-bite' }
    ];
    
    renderCategories(defaultCategories);
}

function createCategoryCard(category) {
    const col = document.createElement('div');
    col.className = 'col-lg-2 col-md-3 col-sm-4 col-6 mb-4';
    
    const iconClass = category.iconUrl || 'fas fa-utensils';
    
    col.innerHTML = `
        <div class="card category-card h-100" onclick="browseCategory(${category.id})">
            <div class="card-body text-center">
                <i class="${iconClass} category-icon"></i>
                <h6 class="card-title">${category.name}</h6>
            </div>
        </div>
    `;
    
    return col;
}

function loadMerchants() {
    // 模拟商家数据
    const merchants = [
        { id: 1, name: '川味小厨', rating: 4.5, status: 'approved' },
        { id: 2, name: '意式风情', rating: 4.3, status: 'approved' },
        { id: 3, name: '日式料理', rating: 4.7, status: 'approved' },
        { id: 4, name: '粤菜馆', rating: 4.2, status: 'approved' }
    ];
    
    renderMerchants(merchants);
}

function renderMerchants(merchants) {
    const container = document.getElementById('merchantCards');
    container.innerHTML = '';
    
    merchants.forEach(merchant => {
        const card = createMerchantCard(merchant);
        container.appendChild(card);
    });
}

function createMerchantCard(merchant) {
    const col = document.createElement('div');
    col.className = 'col-lg-3 col-md-4 col-sm-6 mb-4';
    
    col.innerHTML = `
        <div class="card merchant-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="card-title">${merchant.name}</h6>
                    <span class="merchant-status status-${merchant.status}">营业中</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <div class="meal-rating">
                        ${utils.generateStars(merchant.rating)}
                        <small class="text-muted">${merchant.rating}</small>
                    </div>
                    <button class="btn btn-outline-primary btn-sm" onclick="browseMerchant(${merchant.id})">
                        查看菜单
                    </button>
                </div>
            </div>
        </div>
    `;
    
    return col;
}

// 交互函数
function addToCart(mealId) {
    if (!authToken) {
        utils.showToast('请先登录', 'warning');
        document.getElementById('loginBtn').click();
        return;
    }
    
    utils.showToast('已添加到购物车', 'success');
    // 这里可以实现实际的购物车逻辑
}

function browseCategory(categoryId) {
    utils.showToast(`浏览分类 ${categoryId}`, 'info');
    // 这里可以跳转到分类页面
}

function browseMerchant(merchantId) {
    utils.showToast(`查看商家 ${merchantId}`, 'info');
    // 这里可以跳转到商家页面
}

// 导出工具函数供其他页面使用
window.utils = utils;
window.api = api;
