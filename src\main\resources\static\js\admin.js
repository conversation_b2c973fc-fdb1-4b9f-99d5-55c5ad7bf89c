// 管理员页面JavaScript
let revenueChart = null;
let categoryChart = null;

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeAdminPage();
});

function initializeAdminPage() {
    // 检查管理员权限
    checkAdminPermission();
    
    // 绑定事件监听器
    bindAdminEventListeners();
    
    // 加载仪表板数据
    loadDashboardData();
    
    // 初始化图表
    initializeCharts();
}

function checkAdminPermission() {
    const authToken = localStorage.getItem('authToken');
    if (!authToken) {
        window.location.href = '/index.html';
        return;
    }
    
    // 这里可以添加更严格的权限检查
    // 例如验证用户是否具有管理员角色
}

function bindAdminEventListeners() {
    // 侧边栏切换
    document.getElementById('sidebarToggle').addEventListener('click', toggleSidebar);
    
    // 导航链接点击
    document.querySelectorAll('.sidebar-nav .nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.getAttribute('data-section');
            switchSection(section);
        });
    });
    
    // 退出登录
    document.getElementById('logoutBtn').addEventListener('click', function(e) {
        e.preventDefault();
        logout();
    });
}

function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.querySelector('.main-content');
    
    sidebar.classList.toggle('collapsed');
    mainContent.classList.toggle('expanded');
}

function switchSection(sectionName) {
    // 隐藏所有内容区域
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // 显示选中的内容区域
    const targetSection = document.getElementById(`${sectionName}-section`);
    if (targetSection) {
        targetSection.classList.add('active');
    }
    
    // 更新导航状态
    document.querySelectorAll('.sidebar-nav .nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    const activeLink = document.querySelector(`[data-section="${sectionName}"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }
    
    // 更新页面标题
    document.getElementById('currentSection').textContent = getSectionTitle(sectionName);
    
    // 根据不同区域加载相应数据
    loadSectionData(sectionName);
}

function getSectionTitle(sectionName) {
    const titles = {
        'dashboard': '仪表板',
        'users': '用户管理',
        'merchants': '商家管理',
        'meals': '商品管理',
        'orders': '订单管理',
        'analytics': '数据分析',
        'recommendations': '推荐系统',
        'settings': '系统设置'
    };
    return titles[sectionName] || '未知页面';
}

function loadSectionData(sectionName) {
    switch (sectionName) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'users':
            loadUsersData();
            break;
        case 'merchants':
            loadMerchantsData();
            break;
        case 'meals':
            loadMealsData();
            break;
        case 'orders':
            loadOrdersData();
            break;
        case 'analytics':
            loadAnalyticsData();
            break;
        case 'recommendations':
            loadRecommendationsData();
            break;
        case 'settings':
            loadSettingsData();
            break;
    }
}

async function loadDashboardData() {
    try {
        // 加载实时数据
        const response = await fetch('/api/analytics/dashboard', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('authToken')}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            updateDashboardStats(data.data);
        } else {
            // 使用模拟数据
            updateDashboardStats({
                todayOrders: 156,
                todayRevenue: 12580.50,
                onlineUsers: 23,
                pendingOrders: 8
            });
        }
        
        // 加载图表数据
        loadChartData();
        
        // 加载最近订单
        loadRecentOrders();
        
    } catch (error) {
        console.error('加载仪表板数据失败:', error);
        // 使用模拟数据
        updateDashboardStats({
            todayOrders: 156,
            todayRevenue: 12580.50,
            onlineUsers: 23,
            pendingOrders: 8
        });
    }
}

function updateDashboardStats(stats) {
    document.getElementById('todayOrders').textContent = stats.todayOrders || 0;
    document.getElementById('todayRevenue').textContent = `¥${(stats.todayRevenue || 0).toFixed(2)}`;
    document.getElementById('onlineUsers').textContent = stats.onlineUsers || 0;
    document.getElementById('pendingOrders').textContent = stats.pendingOrders || 0;
}

function initializeCharts() {
    // 初始化收入趋势图
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    revenueChart = new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '收入',
                data: [],
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '¥' + value;
                        }
                    }
                }
            }
        }
    });
    
    // 初始化分类销售饼图
    const categoryCtx = document.getElementById('categoryChart').getContext('2d');
    categoryChart = new Chart(categoryCtx, {
        type: 'doughnut',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [
                    '#4e73df',
                    '#1cc88a',
                    '#36b9cc',
                    '#f6c23e',
                    '#e74a3b',
                    '#858796'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

async function loadChartData() {
    try {
        // 加载收入趋势数据
        const revenueResponse = await fetch('/api/charts/revenue-trend?days=7', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('authToken')}`
            }
        });
        
        if (revenueResponse.ok) {
            const revenueData = await revenueResponse.json();
            updateRevenueChart(revenueData.data);
        } else {
            // 使用模拟数据
            updateRevenueChart({
                labels: ['01-15', '01-16', '01-17', '01-18', '01-19', '01-20', '01-21'],
                data: [1200, 1900, 3000, 5000, 2000, 3000, 4500]
            });
        }
        
        // 加载分类销售数据
        const categoryResponse = await fetch('/api/charts/category-sales?days=30', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('authToken')}`
            }
        });
        
        if (categoryResponse.ok) {
            const categoryData = await categoryResponse.json();
            updateCategoryChart(categoryData.data);
        } else {
            // 使用模拟数据
            updateCategoryChart({
                labels: ['中式菜品', '西式菜品', '日韩料理', '饮品', '甜品'],
                data: [35, 25, 20, 12, 8]
            });
        }
        
    } catch (error) {
        console.error('加载图表数据失败:', error);
    }
}

function updateRevenueChart(data) {
    if (revenueChart) {
        revenueChart.data.labels = data.labels;
        revenueChart.data.datasets[0].data = data.data;
        revenueChart.update();
    }
}

function updateCategoryChart(data) {
    if (categoryChart) {
        categoryChart.data.labels = data.labels;
        categoryChart.data.datasets[0].data = data.data;
        categoryChart.update();
    }
}

async function loadRecentOrders() {
    try {
        // 这里应该调用实际的API
        // const response = await fetch('/api/orders/recent', {...});
        
        // 使用模拟数据
        const orders = [
            {
                orderNumber: 'ORD001',
                customerName: '张三',
                merchantName: '川味小厨',
                amount: 68.50,
                status: 'PENDING',
                createdAt: '2024-01-21 14:30:00'
            },
            {
                orderNumber: 'ORD002',
                customerName: '李四',
                merchantName: '意式风情',
                amount: 125.00,
                status: 'DELIVERED',
                createdAt: '2024-01-21 14:25:00'
            },
            {
                orderNumber: 'ORD003',
                customerName: '王五',
                merchantName: '日式料理',
                amount: 89.90,
                status: 'PREPARING',
                createdAt: '2024-01-21 14:20:00'
            }
        ];
        
        updateRecentOrdersTable(orders);
        
    } catch (error) {
        console.error('加载最近订单失败:', error);
    }
}

function updateRecentOrdersTable(orders) {
    const tbody = document.querySelector('#recentOrdersTable tbody');
    tbody.innerHTML = '';
    
    orders.forEach(order => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${order.orderNumber}</td>
            <td>${order.customerName}</td>
            <td>${order.merchantName}</td>
            <td>¥${order.amount.toFixed(2)}</td>
            <td><span class="badge badge-${getStatusBadgeClass(order.status)}">${getStatusText(order.status)}</span></td>
            <td>${order.createdAt}</td>
        `;
        tbody.appendChild(row);
    });
}

function getStatusBadgeClass(status) {
    const statusClasses = {
        'PENDING': 'warning',
        'PREPARING': 'info',
        'DELIVERING': 'primary',
        'DELIVERED': 'success',
        'CANCELLED': 'danger'
    };
    return statusClasses[status] || 'secondary';
}

function getStatusText(status) {
    const statusTexts = {
        'PENDING': '待处理',
        'PREPARING': '准备中',
        'DELIVERING': '配送中',
        'DELIVERED': '已完成',
        'CANCELLED': '已取消'
    };
    return statusTexts[status] || '未知';
}

// 其他页面的数据加载函数（占位符）
function loadUsersData() {
    console.log('加载用户数据');
}

function loadMerchantsData() {
    console.log('加载商家数据');
}

function loadMealsData() {
    console.log('加载商品数据');
}

function loadOrdersData() {
    console.log('加载订单数据');
}

function loadAnalyticsData() {
    console.log('加载分析数据');
}

function loadRecommendationsData() {
    console.log('加载推荐数据');
}

function loadSettingsData() {
    console.log('加载设置数据');
}

function logout() {
    localStorage.removeItem('authToken');
    window.location.href = '/index.html';
}
