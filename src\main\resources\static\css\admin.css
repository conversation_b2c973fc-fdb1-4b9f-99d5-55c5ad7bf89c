/* 管理员界面样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fc;
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 250px;
    background: linear-gradient(180deg, #4e73df 10%, #224abe 100%);
    z-index: 1000;
    transition: all 0.3s ease;
}

.sidebar.collapsed {
    width: 80px;
}

.sidebar-header {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

.sidebar-header h4 {
    color: white;
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.sidebar.collapsed .sidebar-header h4 {
    display: none;
}

.sidebar-nav {
    padding: 1rem 0;
}

.sidebar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1rem;
    margin: 0.25rem 0;
    border-radius: 0;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.sidebar-nav .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    border-left-color: #fff;
}

.sidebar-nav .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.2);
    border-left-color: #fff;
}

.sidebar.collapsed .sidebar-nav .nav-link {
    text-align: center;
    padding: 0.75rem 0.5rem;
}

.sidebar.collapsed .sidebar-nav .nav-link span {
    display: none;
}

/* 主要内容区域 */
.main-content {
    margin-left: 250px;
    min-height: 100vh;
    transition: all 0.3s ease;
}

.main-content.expanded {
    margin-left: 80px;
}

/* 顶部导航栏 */
.navbar {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

/* 内容区域 */
.content-area {
    padding: 2rem;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* 统计卡片样式 */
.card {
    border: none;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.text-xs {
    font-size: 0.7rem;
}

.font-weight-bold {
    font-weight: 700;
}

.text-uppercase {
    text-transform: uppercase;
}

.text-primary {
    color: #4e73df !important;
}

.text-success {
    color: #1cc88a !important;
}

.text-info {
    color: #36b9cc !important;
}

.text-warning {
    color: #f6c23e !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

/* 图表区域 */
.chart-area {
    position: relative;
    height: 20rem;
    width: 100%;
}

.chart-pie {
    position: relative;
    height: 15rem;
    width: 100%;
}

/* 表格样式 */
.table {
    color: #858796;
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 1px solid #e3e6f0;
    background-color: #f8f9fc;
    color: #6e707e;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
}

.table td {
    border-top: 1px solid #e3e6f0;
    vertical-align: middle;
}

/* 按钮样式 */
.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}

.btn-success {
    background-color: #1cc88a;
    border-color: #1cc88a;
}

.btn-success:hover {
    background-color: #17a673;
    border-color: #169b6b;
}

.btn-info {
    background-color: #36b9cc;
    border-color: #36b9cc;
}

.btn-info:hover {
    background-color: #2c9faf;
    border-color: #2a96a5;
}

.btn-warning {
    background-color: #f6c23e;
    border-color: #f6c23e;
}

.btn-warning:hover {
    background-color: #f4b619;
    border-color: #f4b30d;
}

/* 状态徽章 */
.badge-success {
    background-color: #1cc88a;
}

.badge-warning {
    background-color: #f6c23e;
}

.badge-danger {
    background-color: #e74a3b;
}

.badge-info {
    background-color: #36b9cc;
}

.badge-secondary {
    background-color: #858796;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .content-area {
        padding: 1rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* 加载状态 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #4e73df;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #858796;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #dddfeb;
}

/* 工具提示 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: #5a5c69;
    border-radius: 0.35rem;
}

/* 下拉菜单 */
.dropdown-menu {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 0.35rem;
}

.dropdown-item {
    color: #3a3b45;
    font-size: 0.85rem;
}

.dropdown-item:hover {
    background-color: #f8f9fc;
    color: #3a3b45;
}

/* 分页 */
.pagination .page-link {
    color: #4e73df;
    border-color: #dddfeb;
}

.pagination .page-item.active .page-link {
    background-color: #4e73df;
    border-color: #4e73df;
}

/* 表单控件 */
.form-control {
    border-color: #d1d3e2;
    border-radius: 0.35rem;
}

.form-control:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* 模态框 */
.modal-content {
    border: none;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.modal-header {
    border-bottom-color: #e3e6f0;
}

.modal-footer {
    border-top-color: #e3e6f0;
}
