# 测试环境配置
spring:
  # 数据源配置 - 使用H2内存数据库
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: false
        
  # H2控制台
  h2:
    console:
      enabled: true
      
  # Redis配置 - 使用嵌入式Redis
  redis:
    host: localhost
    port: 6370  # 使用不同端口避免冲突
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        
  # 缓存配置
  cache:
    type: simple  # 使用简单缓存
    
  # 邮件配置 - 禁用
  mail:
    host: localhost
    port: 25
    username: test
    password: test
    properties:
      mail:
        smtp:
          auth: false
          starttls:
            enable: false

# JWT配置
jwt:
  secret: testSecretKey123456789012345678901234567890
  expiration: 3600000 # 1小时

# 推荐系统配置
recommendation:
  personalized-weights:
    category-preference: 0.4
    tag-preference: 0.3
    rating-weight: 0.2
    sales-weight: 0.1
  similarity-weights:
    category-weight: 0.4
    tag-weight: 0.3
    price-weight: 0.2
    rating-weight: 0.1
  algorithm-config:
    user-similarity-threshold: 0.1
    max-similar-users: 10
    high-rating-threshold: 4.0
    recent-days: 7
    max-recommendations: 50
  cache-config:
    personalized-cache-ttl: 300  # 5分钟（测试环境缩短）
    popular-cache-ttl: 180       # 3分钟
    similar-cache-ttl: 600       # 10分钟
    realtime-cache-ttl: 60       # 1分钟

# 日志配置
logging:
  level:
    com.foodorder: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    
# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 测试专用配置
test:
  data:
    cleanup: true  # 测试后清理数据
    mock-external-services: true  # 模拟外部服务
