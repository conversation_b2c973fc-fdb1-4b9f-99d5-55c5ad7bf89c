package com.foodorder.service;

import com.foodorder.common.exception.BusinessException;
import com.foodorder.dto.auth.RegisterRequest;
import com.foodorder.entity.User;
import com.foodorder.entity.UserRole;
import com.foodorder.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserServiceTest {
    
    @Mock
    private UserRepository userRepository;
    
    @Mock
    private PasswordEncoder passwordEncoder;
    
    @Mock
    private EmailService emailService;
    
    @InjectMocks
    private UserService userService;
    
    private RegisterRequest registerRequest;
    private User user;
    
    @BeforeEach
    void setUp() {
        registerRequest = new RegisterRequest();
        registerRequest.setUsername("testuser");
        registerRequest.setPassword("password123");
        registerRequest.setConfirmPassword("password123");
        registerRequest.setEmail("<EMAIL>");
        registerRequest.setRealName("Test User");
        
        user = new User();
        user.setId(1L);
        user.setUsername("testuser");
        user.setEmail("<EMAIL>");
        user.setPassword("encodedPassword");
        user.setRoles(Set.of(UserRole.CUSTOMER));
        user.setEnabled(true);
    }
    
    @Test
    void testRegisterUser_Success() {
        // Given
        when(userRepository.existsByUsername(anyString())).thenReturn(false);
        when(userRepository.existsByEmail(anyString())).thenReturn(false);
        when(passwordEncoder.encode(anyString())).thenReturn("encodedPassword");
        when(userRepository.save(any(User.class))).thenReturn(user);
        
        // When
        User result = userService.registerUser(registerRequest);
        
        // Then
        assertNotNull(result);
        assertEquals("testuser", result.getUsername());
        assertEquals("<EMAIL>", result.getEmail());
        assertTrue(result.getRoles().contains(UserRole.CUSTOMER));
        
        verify(userRepository).existsByUsername("testuser");
        verify(userRepository).existsByEmail("<EMAIL>");
        verify(passwordEncoder).encode("password123");
        verify(userRepository).save(any(User.class));
        verify(emailService).sendWelcomeEmail(any(User.class));
    }
    
    @Test
    void testRegisterUser_UsernameExists() {
        // Given
        when(userRepository.existsByUsername(anyString())).thenReturn(true);
        
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> userService.registerUser(registerRequest));
        
        assertEquals("用户名已存在", exception.getMessage());
        verify(userRepository).existsByUsername("testuser");
        verify(userRepository, never()).save(any(User.class));
    }
    
    @Test
    void testRegisterUser_EmailExists() {
        // Given
        when(userRepository.existsByUsername(anyString())).thenReturn(false);
        when(userRepository.existsByEmail(anyString())).thenReturn(true);
        
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> userService.registerUser(registerRequest));
        
        assertEquals("邮箱已被注册", exception.getMessage());
        verify(userRepository).existsByEmail("<EMAIL>");
        verify(userRepository, never()).save(any(User.class));
    }
    
    @Test
    void testRegisterUser_PasswordMismatch() {
        // Given
        registerRequest.setConfirmPassword("differentPassword");
        
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> userService.registerUser(registerRequest));
        
        assertEquals("两次输入的密码不一致", exception.getMessage());
        verify(userRepository, never()).save(any(User.class));
    }
    
    @Test
    void testFindByUsername_Success() {
        // Given
        when(userRepository.findByUsername(anyString())).thenReturn(Optional.of(user));
        
        // When
        Optional<User> result = userService.findByUsername("testuser");
        
        // Then
        assertTrue(result.isPresent());
        assertEquals("testuser", result.get().getUsername());
        verify(userRepository).findByUsername("testuser");
    }
    
    @Test
    void testFindByUsername_NotFound() {
        // Given
        when(userRepository.findByUsername(anyString())).thenReturn(Optional.empty());
        
        // When
        Optional<User> result = userService.findByUsername("nonexistent");
        
        // Then
        assertFalse(result.isPresent());
        verify(userRepository).findByUsername("nonexistent");
    }
    
    @Test
    void testFindByEmail_Success() {
        // Given
        when(userRepository.findByEmail(anyString())).thenReturn(Optional.of(user));
        
        // When
        Optional<User> result = userService.findByEmail("<EMAIL>");
        
        // Then
        assertTrue(result.isPresent());
        assertEquals("<EMAIL>", result.get().getEmail());
        verify(userRepository).findByEmail("<EMAIL>");
    }
    
    @Test
    void testUpdateUserProfile_Success() {
        // Given
        when(userRepository.findById(anyLong())).thenReturn(Optional.of(user));
        when(userRepository.save(any(User.class))).thenReturn(user);
        
        // When
        User updatedUser = userService.updateUserProfile(1L, "New Name", "<EMAIL>", "1234567890", "New Address");
        
        // Then
        assertNotNull(updatedUser);
        verify(userRepository).findById(1L);
        verify(userRepository).save(any(User.class));
    }
    
    @Test
    void testChangePassword_Success() {
        // Given
        when(userRepository.findById(anyLong())).thenReturn(Optional.of(user));
        when(passwordEncoder.matches(anyString(), anyString())).thenReturn(true);
        when(passwordEncoder.encode(anyString())).thenReturn("newEncodedPassword");
        when(userRepository.save(any(User.class))).thenReturn(user);
        
        // When
        userService.changePassword(1L, "password123", "newPassword123");
        
        // Then
        verify(userRepository).findById(1L);
        verify(passwordEncoder).matches("password123", "encodedPassword");
        verify(passwordEncoder).encode("newPassword123");
        verify(userRepository).save(any(User.class));
    }
    
    @Test
    void testChangePassword_WrongCurrentPassword() {
        // Given
        when(userRepository.findById(anyLong())).thenReturn(Optional.of(user));
        when(passwordEncoder.matches(anyString(), anyString())).thenReturn(false);
        
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> userService.changePassword(1L, "wrongPassword", "newPassword123"));
        
        assertEquals("当前密码不正确", exception.getMessage());
        verify(passwordEncoder).matches("wrongPassword", "encodedPassword");
        verify(userRepository, never()).save(any(User.class));
    }
    
    @Test
    void testEnableUser_Success() {
        // Given
        user.setEnabled(false);
        when(userRepository.findById(anyLong())).thenReturn(Optional.of(user));
        when(userRepository.save(any(User.class))).thenReturn(user);
        
        // When
        userService.enableUser(1L);
        
        // Then
        verify(userRepository).findById(1L);
        verify(userRepository).save(any(User.class));
    }
    
    @Test
    void testDisableUser_Success() {
        // Given
        when(userRepository.findById(anyLong())).thenReturn(Optional.of(user));
        when(userRepository.save(any(User.class))).thenReturn(user);
        
        // When
        userService.disableUser(1L);
        
        // Then
        verify(userRepository).findById(1L);
        verify(userRepository).save(any(User.class));
    }
}
