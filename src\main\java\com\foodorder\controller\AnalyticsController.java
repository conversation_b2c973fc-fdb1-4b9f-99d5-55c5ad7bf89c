package com.foodorder.controller;

import com.foodorder.common.response.ApiResponse;
import com.foodorder.service.DataAnalyticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/analytics")
@Tag(name = "数据分析", description = "数据统计和可视化分析接口")
public class AnalyticsController {
    
    @Autowired
    private DataAnalyticsService dataAnalyticsService;
    
    @GetMapping("/dashboard")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MERCHANT')")
    @Operation(summary = "获取实时仪表板", description = "获取实时数据仪表板信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getRealTimeDashboard() {
        Map<String, Object> dashboard = dataAnalyticsService.getRealTimeDashboard();
        return ResponseEntity.ok(ApiResponse.success(dashboard));
    }
    
    @GetMapping("/sales")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MERCHANT')")
    @Operation(summary = "获取销售统计", description = "获取指定时间段的销售数据统计")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSalesStatistics(
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "7") int days) {
        
        Map<String, Object> stats = dataAnalyticsService.getSalesStatistics(days);
        return ResponseEntity.ok(ApiResponse.success(stats));
    }
    
    @GetMapping("/users")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "获取用户行为分析", description = "获取用户行为和活跃度分析数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserBehaviorAnalysis(
            @RequestParam(defaultValue = "30") int days) {
        
        Map<String, Object> analysis = dataAnalyticsService.getUserBehaviorAnalysis(days);
        return ResponseEntity.ok(ApiResponse.success(analysis));
    }
    
    @GetMapping("/meals")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MERCHANT')")
    @Operation(summary = "获取商品销售分析", description = "获取商品销售情况和热销商品分析")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getMealSalesAnalysis(
            @RequestParam(defaultValue = "30") int days) {
        
        Map<String, Object> analysis = dataAnalyticsService.getMealSalesAnalysis(days);
        return ResponseEntity.ok(ApiResponse.success(analysis));
    }
    
    @GetMapping("/recommendations")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "获取推荐效果分析", description = "获取推荐系统性能和效果分析数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getRecommendationPerformance(
            @RequestParam(defaultValue = "7") int days) {
        
        Map<String, Object> performance = dataAnalyticsService.getRecommendationPerformance(days);
        return ResponseEntity.ok(ApiResponse.success(performance));
    }
    
    @GetMapping("/export/sales")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MERCHANT')")
    @Operation(summary = "导出销售报表", description = "导出指定时间段的销售数据报表")
    public ResponseEntity<ApiResponse<String>> exportSalesReport(
            @RequestParam(defaultValue = "30") int days,
            @RequestParam(defaultValue = "csv") String format) {
        
        // 这里可以实现报表导出功能
        return ResponseEntity.ok(ApiResponse.success("报表导出功能待实现"));
    }
    
    @GetMapping("/charts/revenue-trend")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MERCHANT')")
    @Operation(summary = "获取收入趋势图数据", description = "获取用于绘制收入趋势图的数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getRevenueTrendChart(
            @RequestParam(defaultValue = "30") int days) {
        
        Map<String, Object> stats = dataAnalyticsService.getSalesStatistics(days);
        Map<String, Object> chartData = Map.of(
            "labels", ((Map<String, Object>) stats.get("dailyRevenueTrend")).keySet(),
            "data", ((Map<String, Object>) stats.get("dailyRevenueTrend")).values(),
            "title", "收入趋势图",
            "type", "line"
        );
        
        return ResponseEntity.ok(ApiResponse.success(chartData));
    }
    
    @GetMapping("/charts/order-trend")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MERCHANT')")
    @Operation(summary = "获取订单趋势图数据", description = "获取用于绘制订单趋势图的数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getOrderTrendChart(
            @RequestParam(defaultValue = "30") int days) {
        
        Map<String, Object> stats = dataAnalyticsService.getSalesStatistics(days);
        Map<String, Object> chartData = Map.of(
            "labels", ((Map<String, Object>) stats.get("dailyOrderTrend")).keySet(),
            "data", ((Map<String, Object>) stats.get("dailyOrderTrend")).values(),
            "title", "订单趋势图",
            "type", "line"
        );
        
        return ResponseEntity.ok(ApiResponse.success(chartData));
    }
    
    @GetMapping("/charts/category-sales")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MERCHANT')")
    @Operation(summary = "获取分类销售饼图数据", description = "获取用于绘制分类销售饼图的数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getCategorySalesChart(
            @RequestParam(defaultValue = "30") int days) {
        
        Map<String, Object> analysis = dataAnalyticsService.getMealSalesAnalysis(days);
        Map<String, Object> categorySales = (Map<String, Object>) analysis.get("categorySales");
        
        Map<String, Object> chartData = Map.of(
            "labels", categorySales.keySet(),
            "data", categorySales.values(),
            "title", "分类销售分布",
            "type", "pie"
        );
        
        return ResponseEntity.ok(ApiResponse.success(chartData));
    }
    
    @GetMapping("/charts/user-registration")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "获取用户注册趋势图数据", description = "获取用于绘制用户注册趋势图的数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserRegistrationChart(
            @RequestParam(defaultValue = "30") int days) {
        
        Map<String, Object> analysis = dataAnalyticsService.getUserBehaviorAnalysis(days);
        Map<String, Object> registrationTrend = (Map<String, Object>) analysis.get("registrationTrend");
        
        Map<String, Object> chartData = Map.of(
            "labels", registrationTrend.keySet(),
            "data", registrationTrend.values(),
            "title", "用户注册趋势",
            "type", "bar"
        );
        
        return ResponseEntity.ok(ApiResponse.success(chartData));
    }
    
    @GetMapping("/charts/rating-distribution")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MERCHANT')")
    @Operation(summary = "获取评分分布图数据", description = "获取用于绘制评分分布图的数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getRatingDistributionChart(
            @RequestParam(defaultValue = "30") int days) {
        
        Map<String, Object> analysis = dataAnalyticsService.getUserBehaviorAnalysis(days);
        Map<String, Object> ratingDistribution = (Map<String, Object>) analysis.get("ratingDistribution");
        
        Map<String, Object> chartData = Map.of(
            "labels", ratingDistribution.keySet(),
            "data", ratingDistribution.values(),
            "title", "评分分布",
            "type", "bar"
        );
        
        return ResponseEntity.ok(ApiResponse.success(chartData));
    }
    
    @GetMapping("/summary")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "获取数据概览", description = "获取系统整体数据概览")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getDataSummary() {
        Map<String, Object> dashboard = dataAnalyticsService.getRealTimeDashboard();
        Map<String, Object> salesStats = dataAnalyticsService.getSalesStatistics(30);
        Map<String, Object> userAnalysis = dataAnalyticsService.getUserBehaviorAnalysis(30);
        
        Map<String, Object> summary = Map.of(
            "realTimeData", dashboard,
            "salesOverview", Map.of(
                "totalRevenue", salesStats.get("totalRevenue"),
                "totalOrders", salesStats.get("totalOrders"),
                "completionRate", salesStats.get("completionRate")
            ),
            "userOverview", Map.of(
                "totalUsers", userAnalysis.get("totalUsers"),
                "activeUsers", userAnalysis.get("activeUsers"),
                "activeUserRate", userAnalysis.get("activeUserRate")
            )
        );
        
        return ResponseEntity.ok(ApiResponse.success(summary));
    }
}
