package com.foodorder.config;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuator.metrics.MetricsEndpoint;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Configuration
public class MonitoringConfig {
    
    @Autowired
    private MeterRegistry meterRegistry;
    
    @Bean
    public OncePerRequestFilter requestTimingFilter() {
        return new OncePerRequestFilter() {
            @Override
            protected void doFilterInternal(HttpServletRequest request, 
                                          HttpServletResponse response, 
                                          Filter<PERSON>hain filterChain) throws ServletException, IOException {
                
                Timer.Sample sample = Timer.start(meterRegistry);
                
                try {
                    filterChain.doFilter(request, response);
                } finally {
                    sample.stop(Timer.builder("http.requests")
                        .tag("method", request.getMethod())
                        .tag("uri", request.getRequestURI())
                        .tag("status", String.valueOf(response.getStatus()))
                        .register(meterRegistry));
                }
            }
        };
    }
    
    // 自定义指标
    @Bean
    public Timer recommendationTimer() {
        return Timer.builder("recommendation.requests")
            .description("推荐请求响应时间")
            .register(meterRegistry);
    }
    
    @Bean
    public Timer authTimer() {
        return Timer.builder("auth.requests")
            .description("认证请求响应时间")
            .register(meterRegistry);
    }
}
