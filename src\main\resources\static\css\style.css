/* 全局样式 */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: #333;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: #fff !important;
}

/* 英雄区域样式 */
.hero-section {
    position: relative;
    overflow: hidden;
}

.hero-slide {
    min-height: 60vh;
    display: flex;
    align-items: center;
    position: relative;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

.min-vh-50 {
    min-height: 50vh;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-img-top {
    border-radius: 15px 15px 0 0;
    height: 200px;
    object-fit: cover;
}

/* 美食卡片样式 */
.meal-card {
    position: relative;
    overflow: hidden;
}

.meal-card .badge {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 2;
}

.meal-price {
    font-size: 1.25rem;
    font-weight: bold;
    color: var(--danger-color);
}

.meal-rating {
    color: #ffc107;
}

/* 分类卡片样式 */
.category-card {
    text-align: center;
    padding: 2rem 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.category-card:hover {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.category-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
    transition: color 0.3s ease;
}

.category-card:hover .category-icon {
    color: white;
}

/* 商家卡片样式 */
.merchant-card {
    border-left: 4px solid var(--primary-color);
}

.merchant-status {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: bold;
}

.status-approved {
    background-color: var(--success-color);
    color: white;
}

.status-pending {
    background-color: var(--warning-color);
    color: white;
}

/* 按钮样式 */
.btn {
    border-radius: 25px;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-2px);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* 模态框样式 */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 15px 15px 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 15px 15px;
}

/* 表单样式 */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 页脚样式 */
footer {
    background: linear-gradient(135deg, #343a40 0%, #212529 100%);
}

footer a {
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: var(--primary-color) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hero-slide {
        min-height: 40vh;
        text-align: center;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .display-5 {
        font-size: 1.75rem;
    }
    
    .card-img-top {
        height: 150px;
    }
    
    .category-icon {
        font-size: 2rem;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--dark-color);
    border-radius: 6px;
}

/* 徽章样式 */
.badge {
    border-radius: 20px;
    font-weight: 500;
}

/* 列表样式 */
.list-group-item {
    border: none;
    border-radius: 10px;
    margin-bottom: 0.5rem;
    transition: background-color 0.3s ease;
}

.list-group-item:hover {
    background-color: var(--light-color);
}

/* 进度条样式 */
.progress {
    border-radius: 10px;
    height: 8px;
}

.progress-bar {
    border-radius: 10px;
}

/* 警告框样式 */
.alert {
    border-radius: 10px;
    border: none;
}

/* 分页样式 */
.pagination .page-link {
    border-radius: 20px;
    margin: 0 2px;
    border: none;
    color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 搜索框样式 */
.search-box {
    position: relative;
}

.search-box .form-control {
    padding-left: 2.5rem;
}

.search-box .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary-color);
}

/* 评分星星样式 */
.rating-stars {
    color: #ffc107;
}

.rating-stars .far {
    color: #e9ecef;
}

/* 价格标签样式 */
.price-tag {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-weight: bold;
}

/* 状态指示器 */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-online {
    background-color: var(--success-color);
}

.status-offline {
    background-color: var(--secondary-color);
}

.status-busy {
    background-color: var(--warning-color);
}
