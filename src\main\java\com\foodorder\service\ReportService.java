package com.foodorder.service;

import com.foodorder.entity.*;
import com.foodorder.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ReportService {
    
    private static final Logger logger = LoggerFactory.getLogger(ReportService.class);
    
    @Autowired
    private OrderRepository orderRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private MealRepository mealRepository;
    
    @Autowired
    private RatingRepository ratingRepository;
    
    @Autowired
    private MerchantRepository merchantRepository;
    
    /**
     * 生成销售报表CSV
     */
    public byte[] generateSalesReportCSV(int days) throws IOException {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusDays(days);
        
        List<Order> orders = orderRepository.findByCreatedAtBetween(startDate, endDate);
        
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        PrintWriter writer = new PrintWriter(new OutputStreamWriter(baos, "UTF-8"));
        
        // CSV头部
        writer.println("订单ID,订单号,客户名称,商家名称,订单状态,订单金额,创建时间,完成时间");
        
        // 数据行
        for (Order order : orders) {
            writer.printf("%d,%s,%s,%s,%s,%.2f,%s,%s%n",
                order.getId(),
                order.getOrderNumber(),
                order.getCustomer().getRealName() != null ? order.getCustomer().getRealName() : order.getCustomer().getUsername(),
                order.getMerchant().getName(),
                order.getStatus().toString(),
                order.getTotalAmount(),
                order.getCreatedAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                order.getCompletedAt() != null ? order.getCompletedAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : ""
            );
        }
        
        writer.flush();
        writer.close();
        
        return baos.toByteArray();
    }
    
    /**
     * 生成商品销售报表CSV
     */
    public byte[] generateMealSalesReportCSV(int days) throws IOException {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusDays(days);
        
        List<Order> completedOrders = orderRepository.findByStatusAndCreatedAtBetween(
            OrderStatus.DELIVERED, startDate, endDate);
        
        // 统计商品销售数据
        Map<Long, MealSalesData> mealSalesMap = completedOrders.stream()
            .flatMap(order -> order.getOrderItems().stream())
            .collect(Collectors.groupingBy(
                item -> item.getMeal().getId(),
                Collectors.reducing(
                    new MealSalesData(),
                    item -> new MealSalesData(item.getMeal(), item.getQuantity(), item.getSubtotal()),
                    (data1, data2) -> {
                        data1.quantity += data2.quantity;
                        data1.revenue = data1.revenue.add(data2.revenue);
                        return data1;
                    }
                )
            ));
        
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        PrintWriter writer = new PrintWriter(new OutputStreamWriter(baos, "UTF-8"));
        
        // CSV头部
        writer.println("商品ID,商品名称,分类,商家,销售数量,销售金额,单价");
        
        // 数据行
        mealSalesMap.values().stream()
            .sorted((a, b) -> Integer.compare(b.quantity, a.quantity))
            .forEach(data -> {
                writer.printf("%d,%s,%s,%s,%d,%.2f,%.2f%n",
                    data.meal.getId(),
                    data.meal.getName(),
                    data.meal.getCategory().getName(),
                    data.meal.getMerchant().getName(),
                    data.quantity,
                    data.revenue,
                    data.meal.getPrice()
                );
            });
        
        writer.flush();
        writer.close();
        
        return baos.toByteArray();
    }
    
    /**
     * 生成用户行为报表CSV
     */
    public byte[] generateUserBehaviorReportCSV(int days) throws IOException {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusDays(days);
        
        List<User> users = userRepository.findAll();
        
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        PrintWriter writer = new PrintWriter(new OutputStreamWriter(baos, "UTF-8"));
        
        // CSV头部
        writer.println("用户ID,用户名,真实姓名,注册时间,最后登录时间,订单数量,评分数量,平均评分");
        
        // 数据行
        for (User user : users) {
            long orderCount = orderRepository.countByCustomer(user);
            List<Rating> userRatings = ratingRepository.findByUser(user);
            long ratingCount = userRatings.size();
            
            BigDecimal avgRating = BigDecimal.ZERO;
            if (!userRatings.isEmpty()) {
                avgRating = userRatings.stream()
                    .map(Rating::getScore)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(BigDecimal.valueOf(userRatings.size()), 2, java.math.RoundingMode.HALF_UP);
            }
            
            writer.printf("%d,%s,%s,%s,%s,%d,%d,%.2f%n",
                user.getId(),
                user.getUsername(),
                user.getRealName() != null ? user.getRealName() : "",
                user.getCreatedAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                user.getLastLoginAt() != null ? user.getLastLoginAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "",
                orderCount,
                ratingCount,
                avgRating
            );
        }
        
        writer.flush();
        writer.close();
        
        return baos.toByteArray();
    }
    
    /**
     * 生成商家业绩报表CSV
     */
    public byte[] generateMerchantPerformanceReportCSV(int days) throws IOException {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusDays(days);
        
        List<Order> completedOrders = orderRepository.findByStatusAndCreatedAtBetween(
            OrderStatus.DELIVERED, startDate, endDate);
        
        // 统计商家业绩数据
        Map<Long, MerchantPerformanceData> merchantPerformanceMap = completedOrders.stream()
            .collect(Collectors.groupingBy(
                order -> order.getMerchant().getId(),
                Collectors.reducing(
                    new MerchantPerformanceData(),
                    order -> new MerchantPerformanceData(order.getMerchant(), 1, order.getTotalAmount()),
                    (data1, data2) -> {
                        data1.orderCount += data2.orderCount;
                        data1.revenue = data1.revenue.add(data2.revenue);
                        return data1;
                    }
                )
            ));
        
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        PrintWriter writer = new PrintWriter(new OutputStreamWriter(baos, "UTF-8"));
        
        // CSV头部
        writer.println("商家ID,商家名称,状态,订单数量,总收入,平均订单金额,商品数量,平均评分");
        
        // 数据行
        merchantPerformanceMap.values().stream()
            .sorted((a, b) -> a.revenue.compareTo(b.revenue) * -1)
            .forEach(data -> {
                long mealCount = mealRepository.countByMerchant(data.merchant);
                
                // 计算商家平均评分
                List<Rating> merchantRatings = ratingRepository.findByMealMerchant(data.merchant);
                BigDecimal avgRating = BigDecimal.ZERO;
                if (!merchantRatings.isEmpty()) {
                    avgRating = merchantRatings.stream()
                        .map(Rating::getScore)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .divide(BigDecimal.valueOf(merchantRatings.size()), 2, java.math.RoundingMode.HALF_UP);
                }
                
                BigDecimal avgOrderAmount = data.orderCount > 0 ? 
                    data.revenue.divide(BigDecimal.valueOf(data.orderCount), 2, java.math.RoundingMode.HALF_UP) : 
                    BigDecimal.ZERO;
                
                writer.printf("%d,%s,%s,%d,%.2f,%.2f,%d,%.2f%n",
                    data.merchant.getId(),
                    data.merchant.getName(),
                    data.merchant.getStatus().toString(),
                    data.orderCount,
                    data.revenue,
                    avgOrderAmount,
                    mealCount,
                    avgRating
                );
            });
        
        writer.flush();
        writer.close();
        
        return baos.toByteArray();
    }
    
    /**
     * 生成评分分析报表CSV
     */
    public byte[] generateRatingAnalysisReportCSV(int days) throws IOException {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusDays(days);
        
        List<Rating> ratings = ratingRepository.findByCreatedAtBetween(startDate, endDate);
        
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        PrintWriter writer = new PrintWriter(new OutputStreamWriter(baos, "UTF-8"));
        
        // CSV头部
        writer.println("评分ID,用户名,商品名称,商家名称,评分,评价内容,创建时间");
        
        // 数据行
        for (Rating rating : ratings) {
            writer.printf("%d,%s,%s,%s,%.1f,%s,%s%n",
                rating.getId(),
                rating.getUser().getUsername(),
                rating.getMeal().getName(),
                rating.getMeal().getMerchant().getName(),
                rating.getScore(),
                rating.getReview() != null ? rating.getReview().replace(",", "，").replace("\n", " ") : "",
                rating.getCreatedAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            );
        }
        
        writer.flush();
        writer.close();
        
        return baos.toByteArray();
    }
    
    // 内部数据类
    private static class MealSalesData {
        Meal meal;
        int quantity;
        BigDecimal revenue;
        
        public MealSalesData() {
            this.quantity = 0;
            this.revenue = BigDecimal.ZERO;
        }
        
        public MealSalesData(Meal meal, int quantity, BigDecimal revenue) {
            this.meal = meal;
            this.quantity = quantity;
            this.revenue = revenue;
        }
    }
    
    private static class MerchantPerformanceData {
        Merchant merchant;
        int orderCount;
        BigDecimal revenue;
        
        public MerchantPerformanceData() {
            this.orderCount = 0;
            this.revenue = BigDecimal.ZERO;
        }
        
        public MerchantPerformanceData(Merchant merchant, int orderCount, BigDecimal revenue) {
            this.merchant = merchant;
            this.orderCount = orderCount;
            this.revenue = revenue;
        }
    }
}
