package com.foodorder.service;

import com.foodorder.dto.analytics.ChartDataDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ChartDataService {
    
    @Autowired
    private DataAnalyticsService dataAnalyticsService;
    
    /**
     * 获取收入趋势图数据
     */
    public ChartDataDto getRevenueTrendChart(int days) {
        Map<String, Object> stats = dataAnalyticsService.getSalesStatistics(days);
        Map<String, Object> dailyRevenue = (Map<String, Object>) stats.get("dailyRevenueTrend");
        
        List<String> labels = new ArrayList<>(dailyRevenue.keySet());
        List<Object> data = new ArrayList<>(dailyRevenue.values());
        
        ChartDataDto.DatasetDto dataset = new ChartDataDto.DatasetDto(
            "收入金额", data, "rgba(54, 162, 235, 0.2)", "rgba(54, 162, 235, 1)");
        dataset.setBorderWidth(2);
        dataset.setFill(true);
        dataset.setTension("0.4");
        
        ChartDataDto chartData = new ChartDataDto("收入趋势图", "line", labels, List.of(dataset));
        
        // 设置图表选项
        Map<String, Object> options = new HashMap<>();
        options.put("responsive", true);
        options.put("plugins", Map.of(
            "legend", Map.of("position", "top"),
            "title", Map.of("display", true, "text", "收入趋势分析")
        ));
        options.put("scales", Map.of(
            "y", Map.of("beginAtZero", true, "title", Map.of("display", true, "text", "金额 (元)")),
            "x", Map.of("title", Map.of("display", true, "text", "日期"))
        ));
        
        chartData.setOptions(options);
        return chartData;
    }
    
    /**
     * 获取订单趋势图数据
     */
    public ChartDataDto getOrderTrendChart(int days) {
        Map<String, Object> stats = dataAnalyticsService.getSalesStatistics(days);
        Map<String, Object> dailyOrders = (Map<String, Object>) stats.get("dailyOrderTrend");
        
        List<String> labels = new ArrayList<>(dailyOrders.keySet());
        List<Object> data = new ArrayList<>(dailyOrders.values());
        
        ChartDataDto.DatasetDto dataset = new ChartDataDto.DatasetDto(
            "订单数量", data, "rgba(255, 99, 132, 0.2)", "rgba(255, 99, 132, 1)");
        dataset.setBorderWidth(2);
        dataset.setFill(false);
        
        ChartDataDto chartData = new ChartDataDto("订单趋势图", "line", labels, List.of(dataset));
        
        Map<String, Object> options = new HashMap<>();
        options.put("responsive", true);
        options.put("plugins", Map.of(
            "legend", Map.of("position", "top"),
            "title", Map.of("display", true, "text", "订单趋势分析")
        ));
        options.put("scales", Map.of(
            "y", Map.of("beginAtZero", true, "title", Map.of("display", true, "text", "订单数量")),
            "x", Map.of("title", Map.of("display", true, "text", "日期"))
        ));
        
        chartData.setOptions(options);
        return chartData;
    }
    
    /**
     * 获取分类销售饼图数据
     */
    public ChartDataDto getCategorySalesChart(int days) {
        Map<String, Object> analysis = dataAnalyticsService.getMealSalesAnalysis(days);
        Map<String, Object> categorySales = (Map<String, Object>) analysis.get("categorySales");
        
        List<String> labels = new ArrayList<>(categorySales.keySet());
        List<Object> data = new ArrayList<>(categorySales.values());
        
        // 生成颜色数组
        List<String> colors = generateColors(labels.size());
        
        ChartDataDto.DatasetDto dataset = new ChartDataDto.DatasetDto("销售数量", data);
        dataset.setBackgroundColor(String.join(",", colors));
        dataset.setBorderWidth(1);
        
        ChartDataDto chartData = new ChartDataDto("分类销售分布", "pie", labels, List.of(dataset));
        
        Map<String, Object> options = new HashMap<>();
        options.put("responsive", true);
        options.put("plugins", Map.of(
            "legend", Map.of("position", "right"),
            "title", Map.of("display", true, "text", "商品分类销售分布")
        ));
        
        chartData.setOptions(options);
        return chartData;
    }
    
    /**
     * 获取用户注册趋势图数据
     */
    public ChartDataDto getUserRegistrationChart(int days) {
        Map<String, Object> analysis = dataAnalyticsService.getUserBehaviorAnalysis(days);
        Map<String, Object> registrationTrend = (Map<String, Object>) analysis.get("registrationTrend");
        
        List<String> labels = new ArrayList<>(registrationTrend.keySet());
        List<Object> data = new ArrayList<>(registrationTrend.values());
        
        ChartDataDto.DatasetDto dataset = new ChartDataDto.DatasetDto(
            "注册用户数", data, "rgba(75, 192, 192, 0.6)", "rgba(75, 192, 192, 1)");
        dataset.setBorderWidth(1);
        
        ChartDataDto chartData = new ChartDataDto("用户注册趋势", "bar", labels, List.of(dataset));
        
        Map<String, Object> options = new HashMap<>();
        options.put("responsive", true);
        options.put("plugins", Map.of(
            "legend", Map.of("position", "top"),
            "title", Map.of("display", true, "text", "用户注册趋势分析")
        ));
        options.put("scales", Map.of(
            "y", Map.of("beginAtZero", true, "title", Map.of("display", true, "text", "注册人数")),
            "x", Map.of("title", Map.of("display", true, "text", "日期"))
        ));
        
        chartData.setOptions(options);
        return chartData;
    }
    
    /**
     * 获取评分分布图数据
     */
    public ChartDataDto getRatingDistributionChart(int days) {
        Map<String, Object> analysis = dataAnalyticsService.getUserBehaviorAnalysis(days);
        Map<String, Object> ratingDistribution = (Map<String, Object>) analysis.get("ratingDistribution");
        
        List<String> labels = new ArrayList<>(ratingDistribution.keySet());
        List<Object> data = new ArrayList<>(ratingDistribution.values());
        
        ChartDataDto.DatasetDto dataset = new ChartDataDto.DatasetDto(
            "评分数量", data, "rgba(153, 102, 255, 0.6)", "rgba(153, 102, 255, 1)");
        dataset.setBorderWidth(1);
        
        ChartDataDto chartData = new ChartDataDto("评分分布", "bar", labels, List.of(dataset));
        
        Map<String, Object> options = new HashMap<>();
        options.put("responsive", true);
        options.put("plugins", Map.of(
            "legend", Map.of("position", "top"),
            "title", Map.of("display", true, "text", "用户评分分布")
        ));
        options.put("scales", Map.of(
            "y", Map.of("beginAtZero", true, "title", Map.of("display", true, "text", "评分数量")),
            "x", Map.of("title", Map.of("display", true, "text", "评分等级"))
        ));
        
        chartData.setOptions(options);
        return chartData;
    }
    
    /**
     * 获取综合仪表板图表数据
     */
    public Map<String, ChartDataDto> getDashboardCharts() {
        Map<String, ChartDataDto> charts = new HashMap<>();
        
        charts.put("revenueTrend", getRevenueTrendChart(7));
        charts.put("orderTrend", getOrderTrendChart(7));
        charts.put("categorySales", getCategorySalesChart(30));
        charts.put("userRegistration", getUserRegistrationChart(30));
        charts.put("ratingDistribution", getRatingDistributionChart(30));
        
        return charts;
    }
    
    /**
     * 获取商家专用图表数据
     */
    public Map<String, ChartDataDto> getMerchantCharts(Long merchantId) {
        // 这里可以根据商家ID过滤数据
        Map<String, ChartDataDto> charts = new HashMap<>();
        
        // 简化实现，实际应该根据merchantId过滤数据
        charts.put("sales", getRevenueTrendChart(30));
        charts.put("orders", getOrderTrendChart(30));
        
        return charts;
    }
    
    /**
     * 生成颜色数组
     */
    private List<String> generateColors(int count) {
        String[] baseColors = {
            "rgba(255, 99, 132, 0.8)",
            "rgba(54, 162, 235, 0.8)",
            "rgba(255, 205, 86, 0.8)",
            "rgba(75, 192, 192, 0.8)",
            "rgba(153, 102, 255, 0.8)",
            "rgba(255, 159, 64, 0.8)",
            "rgba(199, 199, 199, 0.8)",
            "rgba(83, 102, 255, 0.8)",
            "rgba(255, 99, 255, 0.8)",
            "rgba(99, 255, 132, 0.8)"
        };
        
        List<String> colors = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            colors.add(baseColors[i % baseColors.length]);
        }
        
        return colors;
    }
}
