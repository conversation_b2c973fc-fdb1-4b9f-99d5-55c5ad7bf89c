<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能订餐系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-utensils me-2"></i>智能订餐系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#home">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#meals">美食</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#orders">订单</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">关于</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="loginBtn">
                            <i class="fas fa-sign-in-alt me-1"></i>登录
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="registerBtn">
                            <i class="fas fa-user-plus me-1"></i>注册
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main>
        <!-- 首页轮播图 -->
        <section id="home" class="hero-section">
            <div id="heroCarousel" class="carousel slide" data-bs-ride="carousel">
                <div class="carousel-indicators">
                    <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="0" class="active"></button>
                    <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="1"></button>
                    <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="2"></button>
                </div>
                <div class="carousel-inner">
                    <div class="carousel-item active">
                        <div class="hero-slide bg-gradient-primary">
                            <div class="container">
                                <div class="row align-items-center min-vh-50">
                                    <div class="col-lg-6">
                                        <h1 class="display-4 text-white fw-bold">美味佳肴，一键订购</h1>
                                        <p class="lead text-white-50 mb-4">智能推荐，个性化定制，让您享受最贴心的订餐体验</p>
                                        <button class="btn btn-light btn-lg">立即体验</button>
                                    </div>
                                    <div class="col-lg-6">
                                        <img src="/images/hero-food-1.jpg" alt="美食" class="img-fluid rounded">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="carousel-item">
                        <div class="hero-slide bg-gradient-success">
                            <div class="container">
                                <div class="row align-items-center min-vh-50">
                                    <div class="col-lg-6">
                                        <h1 class="display-4 text-white fw-bold">智能推荐系统</h1>
                                        <p class="lead text-white-50 mb-4">基于您的喜好和历史订单，为您推荐最合适的美食</p>
                                        <button class="btn btn-light btn-lg">了解更多</button>
                                    </div>
                                    <div class="col-lg-6">
                                        <img src="/images/hero-ai.jpg" alt="智能推荐" class="img-fluid rounded">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="carousel-item">
                        <div class="hero-slide bg-gradient-warning">
                            <div class="container">
                                <div class="row align-items-center min-vh-50">
                                    <div class="col-lg-6">
                                        <h1 class="display-4 text-white fw-bold">快速配送</h1>
                                        <p class="lead text-white-50 mb-4">30分钟内送达，新鲜热乎的美食直达您的餐桌</p>
                                        <button class="btn btn-light btn-lg">查看配送范围</button>
                                    </div>
                                    <div class="col-lg-6">
                                        <img src="/images/hero-delivery.jpg" alt="快速配送" class="img-fluid rounded">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon"></span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon"></span>
                </button>
            </div>
        </section>

        <!-- 推荐美食 -->
        <section id="recommendations" class="py-5">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center mb-5">
                        <h2 class="display-5 fw-bold">为您推荐</h2>
                        <p class="lead text-muted">基于智能算法为您精选的美食</p>
                    </div>
                </div>
                <div class="row" id="recommendationCards">
                    <!-- 推荐卡片将通过JavaScript动态加载 -->
                </div>
            </div>
        </section>

        <!-- 美食分类 -->
        <section id="categories" class="py-5 bg-light">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center mb-5">
                        <h2 class="display-5 fw-bold">美食分类</h2>
                        <p class="lead text-muted">丰富多样的美食选择</p>
                    </div>
                </div>
                <div class="row" id="categoryCards">
                    <!-- 分类卡片将通过JavaScript动态加载 -->
                </div>
            </div>
        </section>

        <!-- 热门商家 -->
        <section id="merchants" class="py-5">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center mb-5">
                        <h2 class="display-5 fw-bold">热门商家</h2>
                        <p class="lead text-muted">精选优质商家，品质保证</p>
                    </div>
                </div>
                <div class="row" id="merchantCards">
                    <!-- 商家卡片将通过JavaScript动态加载 -->
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5><i class="fas fa-utensils me-2"></i>智能订餐系统</h5>
                    <p class="text-muted">为您提供最优质的订餐体验，智能推荐，快速配送。</p>
                </div>
                <div class="col-lg-2 mb-4">
                    <h6>快速链接</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-muted">首页</a></li>
                        <li><a href="#" class="text-muted">美食</a></li>
                        <li><a href="#" class="text-muted">订单</a></li>
                        <li><a href="#" class="text-muted">关于我们</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 mb-4">
                    <h6>客户服务</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-muted">帮助中心</a></li>
                        <li><a href="#" class="text-muted">联系我们</a></li>
                        <li><a href="#" class="text-muted">意见反馈</a></li>
                        <li><a href="#" class="text-muted">配送说明</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 mb-4">
                    <h6>联系信息</h6>
                    <p class="text-muted mb-1"><i class="fas fa-phone me-2"></i>************</p>
                    <p class="text-muted mb-1"><i class="fas fa-envelope me-2"></i><EMAIL></p>
                    <p class="text-muted"><i class="fas fa-map-marker-alt me-2"></i>北京市朝阳区科技园区</p>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2024 智能订餐系统. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-muted me-3"><i class="fab fa-weixin"></i></a>
                    <a href="#" class="text-muted me-3"><i class="fab fa-weibo"></i></a>
                    <a href="#" class="text-muted"><i class="fab fa-qq"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <!-- 登录模态框 -->
    <div class="modal fade" id="loginModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户登录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="loginUsername" class="form-label">用户名/邮箱</label>
                            <input type="text" class="form-control" id="loginUsername" required>
                        </div>
                        <div class="mb-3">
                            <label for="loginPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="loginPassword" required>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">记住我</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="loginSubmit">登录</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册模态框 -->
    <div class="modal fade" id="registerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户注册</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="registerForm">
                        <div class="mb-3">
                            <label for="registerUsername" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="registerUsername" required>
                        </div>
                        <div class="mb-3">
                            <label for="registerEmail" class="form-label">邮箱</label>
                            <input type="email" class="form-control" id="registerEmail" required>
                        </div>
                        <div class="mb-3">
                            <label for="registerPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="registerPassword" required>
                        </div>
                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">确认密码</label>
                            <input type="password" class="form-control" id="confirmPassword" required>
                        </div>
                        <div class="mb-3">
                            <label for="realName" class="form-label">真实姓名</label>
                            <input type="text" class="form-control" id="realName">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="registerSubmit">注册</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/app.js"></script>
</body>
</html>
